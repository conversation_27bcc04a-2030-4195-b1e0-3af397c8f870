import { useCoreHouseholdQuery } from 'services/api/newApi/core/household';
import { useConfig } from 'services/config';
import { useAuthenticationStatus } from 'services/user';

export const useShouldEncryptPin = () => {
  const { isAuthenticated } = useAuthenticationStatus();
  const { data: householdInfo } = useCoreHouseholdQuery(isAuthenticated);
  const { getTechConfig } = useConfig();
  const { usernameWithPinEncryptionRegex } = getTechConfig();
  const techWithPinEncryption = new RegExp(usernameWithPinEncryptionRegex);

  let shouldUsePinEncryption = false;
  if (householdInfo) {
    shouldUsePinEncryption = techWithPinEncryption.test(
      householdInfo.householdExtId,
    );
  }

  return { shouldUsePinEncryption };
};
