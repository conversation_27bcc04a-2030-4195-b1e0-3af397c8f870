import { secondsToMilliseconds } from 'date-fns';
import { useMemo } from 'react';

import { getLiveStatus } from 'utils/dateUtils';
import { useConfig } from 'services/config';
import { useAuthenticationStatus } from 'services/user';
import { useChannelRecording } from 'components/Channel/hooks';
import { checkIfIsNowOrFutureProgram } from 'hooks/useProgramButtonsVisibility';
import { checkIfIsCatchupAvailable } from 'utils/program';

import { UseProgramIconsVisibilityProps } from './types';

export const useProgramIconsVisibility = ({
  programId,
  isSubscribedChannel,
  startDate,
  endDate,
  playFeatures,
  catchupDuration,
  isCatchupDisabled,
  isStartoverDisabled,
  isRecordingAllowed,
}: UseProgramIconsVisibilityProps) => {
  const { isAuthenticated } = useAuthenticationStatus();
  const { isRecordingNow } = useChannelRecording(isAuthenticated, programId);

  const { withVisibleNpvrFeatures } = useConfig().getTechConfig();
  const isCatchupAvailable = checkIfIsCatchupAvailable({
    endDate,
    catchupDuration,
    isCatchupDisabled,
    playFeatures,
    withVisibleNpvrFeatures,
  });

  const isNowOrFutureProgram = checkIfIsNowOrFutureProgram(endDate);

  const isLiveNow = useMemo(() => {
    return getLiveStatus(
      secondsToMilliseconds(startDate),
      secondsToMilliseconds(endDate),
    );
  }, [endDate, startDate]);

  const shouldShowStartoverIcon = useMemo(() => {
    if (!isSubscribedChannel) {
      return false;
    }
    if (!withVisibleNpvrFeatures) {
      return false;
    }
    if (!isNowOrFutureProgram) {
      return false;
    }
    if (!Boolean(isStartoverDisabled) && playFeatures?.otg.isStartOver) {
      return true;
    }
    return false;
  }, [
    isNowOrFutureProgram,
    isStartoverDisabled,
    isSubscribedChannel,
    playFeatures?.otg.isStartOver,
    withVisibleNpvrFeatures,
  ]);

  const shouldShowRecordingIcon = useMemo(() => {
    return (
      isAuthenticated &&
      withVisibleNpvrFeatures &&
      isNowOrFutureProgram &&
      isRecordingAllowed
    );
  }, [
    isAuthenticated,
    isNowOrFutureProgram,
    isRecordingAllowed,
    withVisibleNpvrFeatures,
  ]);

  const shouldShowRecordingNowIcon = useMemo(() => {
    return isRecordingNow && isNowOrFutureProgram;
  }, [isNowOrFutureProgram, isRecordingNow]);

  return {
    shouldShowRecordingIcon,
    shouldShowStartoverIcon,
    shouldShowRecordingNowIcon,
    shouldShowCatchupIcon: isCatchupAvailable,
    shouldShowPlayLiveIcon: isLiveNow,
  };
};
