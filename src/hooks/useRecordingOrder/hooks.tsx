import { useMemo } from 'react';
import { AxiosError } from 'axios';
import { secondsToMilliseconds } from 'date-fns';

import { useGlobalLoaderContext } from 'services/loader';
import { useAuthenticationStatus, useUserProfile } from 'services/user';
import { isFutureTime } from 'utils/dateUtils';
import { Channel } from 'services/api/newApi/live/channels';
import { useConfig } from 'services/config';
import { useToasts } from 'services/toasts';
import { ProgramDetails } from 'services/api/newApi/core/tvguide';
import {
  RecordingStatus,
  useRecordingDeleteMutation,
  useRecordingOrderMutation,
  useRecordingsByChannelQuery,
  useSeriesOrderMutation,
} from 'services/api/newApi/optional/npvr';
import { isTargetConflictError } from 'services/error';

export const useRecordingOrderStatus = (
  program: ProgramDetails,
  programChannel: Channel,
) => {
  const { isAuthenticated } = useAuthenticationStatus();

  const { data: recordingsByChannel } = useRecordingsByChannelQuery({
    channelExtId: programChannel.channelExtId,
  });

  const { isNpvrService: isMultiRecorderActive } = useUserProfile();
  const {
    config: {
      appConfig: {
        techConfig: { withVisibleNpvrFeatures },
      },
    },
  } = useConfig();

  const isRecordingAllowed = useMemo(() => {
    return (
      isAuthenticated &&
      withVisibleNpvrFeatures &&
      isFutureTime(secondsToMilliseconds(program.endTimeUtc)) &&
      !program.properties?.recordingDisabled &&
      programChannel.isRecordingAllowed
    );
  }, [
    isAuthenticated,
    program.endTimeUtc,
    program.properties?.recordingDisabled,
    programChannel.isRecordingAllowed,
    withVisibleNpvrFeatures,
  ]);

  const isRecordingOrdered = recordingsByChannel?.recordingLiteList.some(
    (recording) => recording.programExtId === program.programExtId,
  );

  return {
    isRecordingAllowed,
    isRecordingOrdered,
    isMultiRecorderActive,
  };
};

export const useRecordingOrderHandlers = (
  programExtId: ProgramDetails['programExtId'],
  programChannelId: Channel['channelExtId'],
) => {
  const { setIsLoaderVisible } = useGlobalLoaderContext();
  const { showToast } = useToasts();

  const { mutateAsync: orderRecordingAsync } = useRecordingOrderMutation();
  const { mutateAsync: orderSeriesAsync } = useSeriesOrderMutation();

  const { mutateAsync: deleteRecordingAsync } = useRecordingDeleteMutation();

  const { data: recordingsByChannel } = useRecordingsByChannelQuery({
    channelExtId: programChannelId,
  });

  const onOrderRecording = () => {
    setIsLoaderVisible(true);
    orderRecordingAsync({
      channelExtId: programChannelId,
      programExtId: programExtId,
    })
      .then(() => {
        showToast('ORDER_RECORDING');
      })
      .catch((error: AxiosError) => {
        if (isTargetConflictError(error)) {
          return showToast('ORDER_RECORDING_TICKET_EXIST_ERROR');
        }
        showToast('ORDER_RECORDING_ERROR');
      })
      .finally(() => {
        setIsLoaderVisible(false);
      });
  };

  const onOrderRecordingSeries = () => {
    setIsLoaderVisible(true);
    orderSeriesAsync({
      channelExtId: programChannelId,
      programExtId: programExtId,
    })
      .then(() => {
        showToast('ORDER_SERIES');
      })
      .catch((error: AxiosError) => {
        if (isTargetConflictError(error)) {
          return showToast('ORDER_SERIES_TICKET_EXIST_ERROR');
        }
        showToast('ORDER_SERIES_ERROR');
      })
      .finally(() => {
        setIsLoaderVisible(false);
      });
  };

  const onCancelRecordingOrder = () => {
    const selectedRecording = recordingsByChannel?.recordingLiteList.find(
      (recording) => recording.programExtId === programExtId,
    );

    setIsLoaderVisible(true);
    deleteRecordingAsync({
      recordingExtId: selectedRecording?.recordingExtId || '',
      status: RecordingStatus.SCHEDULED,
    }).finally(() => {
      setIsLoaderVisible(false);
    });
  };

  return {
    onOrderRecording,
    onOrderRecordingSeries,
    onCancelRecordingOrder,
  };
};
