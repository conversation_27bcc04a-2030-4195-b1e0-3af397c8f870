import { Story, Meta } from '@storybook/react';

import { RecordingPreviewGrid } from './RecordingPreviewGrid';

import { NormalizedRecording } from '../types';

export default {
  title: 'Components/RecordingPreviewGrid',
  component: RecordingPreviewGrid,
} as Meta;

const data: NormalizedRecording[] = [];

for (let i = 0; i < 13; i++) {
  data.push({
    name: `${i % 2 === 0 ? 'Default Title' : 'Another Title'}`,
    imageSrc: 'https://picsum.photos/355/200',
    isSeries: false,
    status: 'recorded',
  } as NormalizedRecording);
}

const Template: Story = ({ title, isFetching, handleBackwardButtonClick }) => (
  <RecordingPreviewGrid
    title={title}
    isFetching={isFetching}
    data={data}
    handleBackwardButtonClick={handleBackwardButtonClick}
  />
);

export const Default = Template.bind({});
Default.args = {
  title: 'RecordingPreviewGrid Title',
  isFetching: false,
  handleBackwardButtonClick: () => {},
};
