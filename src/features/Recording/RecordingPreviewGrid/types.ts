import { Dispatch, SetStateAction } from 'react';

import { RecordingSeriesParams } from 'services/api/newApi/optional/npvr';

import { NormalizedRecording } from '../types';

export interface RecordingPreviewGridProps {
  title?: string;
  data: Array<NormalizedRecording>;
  isFetching: boolean;
  handleBackwardButtonClick: () => void;
  setSelectedSeriesParams?: Dispatch<
    SetStateAction<RecordingSeriesParams | undefined>
  >;
  isVisible?: boolean;
}

export interface useRecordingPreviewGridValue {
  data: Array<NormalizedRecording>;
  handleBackwardButtonClick: () => void;
  isScheduled: boolean;
}
