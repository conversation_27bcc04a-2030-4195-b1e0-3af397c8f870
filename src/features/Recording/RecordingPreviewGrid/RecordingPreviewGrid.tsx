import { useMemo } from 'react';

import { Loader } from 'components/Loader';
import { RecordingPreview } from 'features/Recording/RecordingPreview';
import { GridHeader } from 'features/Recording/RecordingPreviewGrid/GridHeader';
import { formatTimestampDate, getTimeRemainingUntilNow } from 'utils/dateUtils';
import { RecordingStatus } from 'services/api/newApi/optional/npvr';

import { useRecordingPreviewGrid } from './hooks';
import { RecordingPreviewGridProps } from './types';
import * as S from './styles';

export const RecordingPreviewGrid = ({
  title,
  data,
  isFetching,
  handleBackwardButtonClick,
  setSelectedSeriesParams,
  isVisible = true,
}: RecordingPreviewGridProps) => {
  const isViewForScheduledRecordings =
    data.length > 0 &&
    data.every((item) => item.status === RecordingStatus.SCHEDULED);

  const {
    sortingOption,
    filterSlides,
    goToSliderView,
    isGridHover,
    setIsGridHover,
    sortedRecordings,
  } = useRecordingPreviewGrid({
    data,
    handleBackwardButtonClick,
    isScheduled: isViewForScheduledRecordings,
  });

  const recordings = useMemo(() => {
    return sortedRecordings?.map((recording) => {
      const {
        recordingExtId,
        recordingSeriesId,
        channelExtId,
        name,
        imageSrc,
        startDateEpg,
        expirationDate,
        recordingSeason,
        status,
      } = recording;

      return (
        <S.Poster key={recordingExtId}>
          <RecordingPreview
            recordingExtId={recordingExtId}
            recordingSeriesId={recordingSeriesId}
            channelExtId={channelExtId}
            title={name || ''}
            src={imageSrc}
            dateOfEmission={formatTimestampDate(startDateEpg || 0)}
            remainingTime={getTimeRemainingUntilNow(
              recording.expirationDate || 0,
            )}
            expirationDate={formatTimestampDate(expirationDate || 0)}
            status={recording.status}
            isSeries={recording.isSeries}
            recordingSeason={recordingSeason}
            setSelectedSeriesParams={() => {
              const areSeriesParamsAvailable =
                status && recordingSeriesId && channelExtId && recordingSeason;

              if (areSeriesParamsAvailable && setSelectedSeriesParams) {
                setSelectedSeriesParams({
                  recordingStatus: status,
                  recordingSeriesId: recordingSeriesId,
                  channelExtId: channelExtId,
                  recordingSeason: recordingSeason,
                });
              }
            }}
          />
        </S.Poster>
      );
    });
  }, [setSelectedSeriesParams, sortedRecordings]);

  return (
    <S.Container
      onMouseEnter={() => setIsGridHover(true)}
      onMouseLeave={() => setIsGridHover(false)}
      $isVisible={isVisible}
    >
      {!isFetching ? (
        <>
          <GridHeader
            sortingOption={sortingOption}
            goToSliderView={goToSliderView}
            isGridHover={isGridHover}
            onChangeSort={filterSlides}
            title={title}
            id='recordings-sort'
            isScheduled={isViewForScheduledRecordings}
          />
          <S.Grid>{recordings}</S.Grid>
        </>
      ) : (
        <Loader />
      )}
    </S.Container>
  );
};
