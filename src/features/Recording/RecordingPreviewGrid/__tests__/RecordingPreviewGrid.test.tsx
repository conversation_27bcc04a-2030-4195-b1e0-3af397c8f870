import { screen } from '@testing-library/react';
import { vi } from 'vitest';

import { render } from 'utils/testing';
import { NormalizedRecording } from 'features/Recording/types';

import { RecordingPreviewGrid } from '../RecordingPreviewGrid';

describe('Components:RecordingPreviewGrid', () => {
  it('should render RecordingPreviewGrid component', () => {
    render(
      <RecordingPreviewGrid
        data={
          [
            {
              recordingExtId: '0000',
              name: 'RECORDING',
              imageSrc: 'https://www.fillmurray.com/355/200',
            },
          ] as NormalizedRecording[]
        }
        isFetching={false}
        handleBackwardButtonClick={vi.fn()}
      />,
    );

    expect(screen.getByText('RECORDING')).toBeInTheDocument();
  });

  it('should render all elements inside RecordingPreviewGrid', () => {
    const gridData = [
      {
        name: 'RECORDING1',
        imageSrc: 'https://www.fillmurray.com/355/200',
        isSeries: false,
      },
      {
        name: 'RECORDING2',
        imageSrc: 'https://www.fillmurray.com/355/200',
        isSeries: false,
      },
      {
        name: 'RECORDING3',
        imageSrc: 'https://www.fillmurray.com/355/200',
        isSeries: false,
      },
    ];
    const { container } = render(
      <RecordingPreviewGrid
        handleBackwardButtonClick={vi.fn()}
        data={gridData as NormalizedRecording[]}
        isFetching={false}
      />,
    );
    const renderedImg = container.querySelectorAll('img');
    renderedImg.forEach((img, i) =>
      expect(img).toHaveAttribute('src', gridData[i].imageSrc),
    );
  });
});
