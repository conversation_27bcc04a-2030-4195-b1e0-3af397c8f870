import { useCallback, useEffect, useMemo, useState } from 'react';

import { useScrollLock } from 'hooks/useScrollLock';
import {
  SortDirection,
  SortingOption,
  useSortedData,
} from 'hooks/useSortedData';
import { useDevice } from 'services/device';

import { useRecordingPreviewGridValue } from './types';

import { prepareRecordingsForSorting } from '../recordingSortUtils';
import { useRecordingSortOrder } from '../RecordingSortOrderContext';
import { RECORDINGS_DEFAULT_SORT_KEY } from '../constants';
import { NormalizedRecording } from '../types';

export const useRecordingPreviewGrid = (
  props: useRecordingPreviewGridValue,
) => {
  const { data: recordings, handleBackwardButtonClick, isScheduled } = props;
  const { isMobileDevice } = useDevice();
  const { unlockScroll } = useScrollLock();

  const [isGridHover, setIsGridHover] = useState(isMobileDevice);
  const { sortedData: sortedRecordings, setNewSortedData } =
    useSortedData<NormalizedRecording>(
      recordings,
      RECORDINGS_DEFAULT_SORT_KEY,
      SortDirection.Descending,
    );

  const {
    activeRecordedSortOption,
    activeScheduledSortOption,
    handleActiveRecordedSortOptionChange,
    handleActiveScheduledSortOptionChange,
  } = useRecordingSortOrder();

  const sortingOption = useMemo(
    () => (isScheduled ? activeScheduledSortOption : activeRecordedSortOption),
    [activeRecordedSortOption, activeScheduledSortOption, isScheduled],
  );

  useEffect(() => {
    const preparedRecordings = prepareRecordingsForSorting(
      recordings,
      sortingOption,
    );
    setNewSortedData(preparedRecordings, sortingOption);
  }, [recordings, setNewSortedData, sortingOption]);

  const filterSlides = (option: SortingOption) => {
    isScheduled
      ? handleActiveScheduledSortOptionChange(option)
      : handleActiveRecordedSortOptionChange(option);

    const preparedRecordings = prepareRecordingsForSorting(recordings, option);
    setNewSortedData(preparedRecordings, option);
  };

  const goToSliderView = useCallback(() => {
    handleBackwardButtonClick();
    unlockScroll();
  }, [handleBackwardButtonClick, unlockScroll]);

  return {
    sortingOption,
    sortedRecordings,
    filterSlides,
    goToSliderView,
    isGridHover,
    setIsGridHover,
  };
};
