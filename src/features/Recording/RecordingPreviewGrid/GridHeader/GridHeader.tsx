import { FC, PropsWithChildren, useMemo } from 'react';

import { IconButton } from 'components/Buttons/IconButton';
import { Select } from 'components/Forms/Select';
import { IconGoBackward } from 'components/Icons';
import { H2 } from 'components/Typography';
import { RECORDING_SORTING_OPTIONS } from 'features/Recording/RecordingSortOrderContext/constants';

import { GridHeaderProps } from './types';
import * as S from './style';

export const GridHeader: FC<PropsWithChildren<GridHeaderProps>> = ({
  sortingOption,
  goToSliderView,
  isGridHover,
  onChangeSort,
  title,
  id,
  isScheduled,
}) => {
  const baseGridHeader = useMemo(() => {
    return (
      <S.HeaderWrapper>
        <S.IconWrapper>
          <IconButton onClick={goToSliderView}>
            <IconGoBackward />
          </IconButton>
        </S.IconWrapper>
        {title && <H2>{title}</H2>}
      </S.HeaderWrapper>
    );
  }, [goToSliderView, title]);

  if (sortingOption && isGridHover && onChangeSort) {
    return (
      <S.GridHeader>
        {baseGridHeader}
        <S.SelectWrapper $isGridHover={isGridHover}>
          <Select
            id={id}
            options={
              isScheduled
                ? RECORDING_SORTING_OPTIONS.SCHEDULED
                : RECORDING_SORTING_OPTIONS.RECORDED
            }
            onChange={onChangeSort}
            value={{
              label: sortingOption?.label,
              value: String(sortingOption?.value),
            }}
          />
        </S.SelectWrapper>
      </S.GridHeader>
    );
  }

  return <S.GridHeader>{baseGridHeader}</S.GridHeader>;
};
