import { Story, Meta } from '@storybook/react';

import { RecordingPreview } from './RecordingPreview';

export default {
  title: 'Components/RecordingPreview',
  component: RecordingPreview,
  argTypes: {
    recordingStatus: {
      options: ['recorded', 'scheduled'],
      control: { type: 'select' },
    },
  },
} as Meta;

const Template: Story = ({
  src,
  title,
  dateOfEmission,
  remainingTime,
  isSeries,
  recordingStatus,
}) => (
  <div style={{ width: '355px' }}>
    <RecordingPreview
      recordingExtId='id'
      src={src}
      title={title}
      dateOfEmission={dateOfEmission}
      remainingTime={remainingTime}
      isSeries={isSeries}
      status={recordingStatus}
    />
  </div>
);

export const Default = Template.bind({});
Default.args = {
  title: 'Default Title',
  src: 'https://placehold.co/355x200',
  recordingStatus: 'recorded',
};

export const Series = Template.bind({});
Series.args = {
  title: 'Default Title',
  src: 'https://placehold.co/355x200',
  isSeries: true,
  recordingStatus: 'recorded',
};

export const WithRemainingTime = Template.bind({});
WithRemainingTime.args = {
  title: 'Default Title',
  src: 'https://placehold.co/355x200',
  remainingTime: '16h 23min',
  recordingStatus: 'recorded',
};

export const WithDateOfEmission = Template.bind({});
WithDateOfEmission.args = {
  title: 'Default Title',
  src: 'https://placehold.co/355x200',
  dateOfEmission: '22.02.2022',
  recordingStatus: 'recorded',
};

export const WithDateOfEmissionAndRemainingTime = Template.bind({});
WithDateOfEmissionAndRemainingTime.args = {
  title: 'Default Title',
  src: 'https://placehold.co/355x200',
  dateOfEmission: '22.02.2022',
  remainingTime: '16h 23min',
  recordingStatus: 'recorded',
};
