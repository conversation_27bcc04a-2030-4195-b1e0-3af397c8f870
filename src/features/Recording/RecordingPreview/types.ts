import { Dispatch, SetStateAction } from 'react';

import { ImageProps } from 'components/Image';
import { ParentControlLevel } from 'services/api/common/types';
import {
  RecordingSeriesParams,
  RecordingStatus,
} from 'services/api/newApi/optional/npvr';

export interface RecordingPreviewProps extends ImageProps {
  recordingExtId?: string;
  recordingSeriesId?: string;
  channelExtId?: string;
  title: string;
  dateOfEmission?: string;
  remainingTime?: string;
  expirationDate?: string;
  isSeries?: boolean;
  status?: RecordingStatus;
  prLevel?: ParentControlLevel;
  setSelectedSeriesParams?: Dispatch<SetStateAction<RecordingSeriesParams>>;
  recordingSeason?: string;
}

export interface useRecordingPreviewValue {
  status?: RecordingStatus;
  recordingSeriesId?: string;
  channelExtId?: string;
  recordingSeason?: string;
  setSelectedSeriesParams?: Dispatch<SetStateAction<RecordingSeriesParams>>;
  recordingExtId?: string;
  isSeries?: boolean;
}

export interface ImageWrapperProps {
  $isSeries?: boolean;
}

export interface ImageOverlayProps {
  $withoutBorder?: boolean;
}
