import { MouseEvent, useCallback, useRef, useState } from 'react';
import { useIntl } from 'react-intl';

import { useOutsideClick } from 'hooks/useOutsideClick';
import { DetailsViewType, useDetailsView } from 'services/detailsView';
import {
  RecordingStatus,
  useRecordingDeleteMutation,
  useSeriesDeleteMutation,
} from 'services/api/newApi/optional/npvr';

import { useRecordingPreviewValue } from './types';
import { messages } from './messages';

export const useRecordingPreview = ({
  recordingExtId,
  recordingSeriesId,
  channelExtId,
  recordingSeason,
  setSelectedSeriesParams,
  status,
  isSeries,
}: useRecordingPreviewValue) => {
  const intl = useIntl();

  const {
    deleteRecordingConfirm,
    deleteScheduledRecordingConfirm,
    deleteSeriesConfirm,
    deleteScheduledSeriesConfirm,
  } = messages;

  const [isHover, setIsHover] = useState(false);
  const [isBeforeDelete, setIsBeforeDelete] = useState(false);
  const beforeDeleteOverlay = useRef<HTMLDivElement>(null);
  useOutsideClick(beforeDeleteOverlay, () => {
    setIsBeforeDelete(false);
  });
  const { setData: setDataDetailsView } = useDetailsView();
  const { mutateAsync: deleteRecordingAsync } = useRecordingDeleteMutation();
  const { mutateAsync: deleteSeriesAsync } = useSeriesDeleteMutation();

  const handleDeleteButton = useCallback((event: MouseEvent) => {
    event.stopPropagation();
    setIsBeforeDelete(true);
  }, []);

  const handleDeleteConfirmed = useCallback(async () => {
    setIsBeforeDelete(false);
    if (recordingExtId && status) {
      isSeries
        ? await deleteSeriesAsync({
            recordingExtId,
            status,
          })
        : await deleteRecordingAsync({
            recordingExtId,
            status,
          });
    }
  }, [
    deleteRecordingAsync,
    deleteSeriesAsync,
    isSeries,
    recordingExtId,
    status,
  ]);

  const handleSelectedSeriesId = useCallback(() => {
    const allSeriesParamsAreAvailable =
      recordingSeriesId && channelExtId && status && recordingSeason;

    if (allSeriesParamsAreAvailable && setSelectedSeriesParams) {
      setSelectedSeriesParams({
        recordingStatus: status,
        recordingSeriesId: recordingSeriesId,
        channelExtId: channelExtId,
        recordingSeason: recordingSeason,
      });
    }
  }, [
    channelExtId,
    recordingSeason,
    recordingSeriesId,
    setSelectedSeriesParams,
    status,
  ]);

  const getDeleteQuestion = useCallback(() => {
    const isScheduledOrInProgress =
      status === RecordingStatus.SCHEDULED ||
      status === RecordingStatus.IN_PROGRESS;

    if (isSeries) {
      return intl.formatMessage(
        isScheduledOrInProgress
          ? deleteScheduledSeriesConfirm
          : deleteSeriesConfirm,
      );
    }

    return intl.formatMessage(
      isScheduledOrInProgress
        ? deleteScheduledRecordingConfirm
        : deleteRecordingConfirm,
    );
  }, [
    deleteRecordingConfirm,
    deleteScheduledRecordingConfirm,
    deleteScheduledSeriesConfirm,
    deleteSeriesConfirm,
    intl,
    isSeries,
    status,
  ]);

  const slideClick = (
    id: string,
    dateEmission: string,
    expirationDate: string,
  ) =>
    setDataDetailsView({
      type: DetailsViewType.Recording,
      id,
      dateEmission,
      expirationDate,
    });

  return {
    setIsHover,
    isHover,
    isBeforeDelete,
    handleDeleteButton,
    handleSelectedSeriesId,
    getDeleteQuestion,
    handleDeleteConfirmed,
    beforeDeleteOverlay,
    slideClick,
  };
};
