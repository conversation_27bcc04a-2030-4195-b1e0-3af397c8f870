import { useTheme } from 'styled-components';

import { usePlayContent } from 'features/Player/Hook';
import { RecordingStatus } from 'services/api/newApi/optional/npvr';
import { IconWarning } from 'components/Icons';
import { Image } from 'components/Image';
import { isPastRemainingTime } from 'utils/dateUtils';
import { useErrorScreen } from 'services/error';

import { RecordingPreviewProps } from './types';
import * as S from './styles';
import { useRecordingPreview } from './hooks';
import { ScheduledSeriesOverlay } from './components/ScheduledSeriesOverlay';
import { RecordedSeriesOverlay } from './components/RecordedSeriesOverlay';
import { ScheduledRecordingOverlay } from './components/ScheduledRecordingOverlay';
import { RecordedRecordingOverlay } from './components/RecordedRecordingOverlay';
import { DeleteConfirmation } from './components/DeletecConfirmation';
import { RecordingPreviewCaption } from './components/RecordingPreviewCaption';
import { FailedRecordingOverlay } from './components/FailedRecordingOverlay';

import { useRecordingChannelFeatures } from '../useRecordingChannelFeatures';

export const RecordingPreview = ({
  recordingExtId,
  recordingSeriesId,
  channelExtId,
  title,
  src,
  dateOfEmission = '',
  remainingTime = '',
  expirationDate = '',
  isSeries = false,
  status,
  recordingSeason,
  setSelectedSeriesParams,
  prLevel,
  ...rest
}: RecordingPreviewProps) => {
  const appTheme = useTheme();
  const {
    setIsHover,
    isHover,
    isBeforeDelete,
    handleDeleteButton,
    handleSelectedSeriesId,
    getDeleteQuestion,
    handleDeleteConfirmed,
    beforeDeleteOverlay,
    slideClick,
  } = useRecordingPreview({
    recordingExtId,
    recordingSeriesId,
    channelExtId,
    recordingSeason,
    setSelectedSeriesParams,
    status,
    isSeries,
  });

  const { isNpvrForRecordingChannelDisabled } = useRecordingChannelFeatures({
    recordingchannelExtId: channelExtId,
  });

  const { showErrorModal } = useErrorScreen();
  const { playRecording } = usePlayContent();
  const onPlayClick = () => {
    if (channelExtId && recordingExtId) {
      playRecording({
        channelExtId: channelExtId,
        recordingExtId,
        programParentalControlLevel: prLevel,
      });
    } else {
      showErrorModal('PLAYER_ERROR');
    }
  };

  const isRecordingExpired = isPastRemainingTime(remainingTime);
  const shouldShowRemainingTime =
    remainingTime.length > 0 && status !== RecordingStatus.SCHEDULED;
  const isRecordingFailed = !isSeries && status === RecordingStatus.FAILED;

  const handleOpenRecordingDetails = () => {
    recordingExtId &&
      slideClick(recordingExtId, dateOfEmission, expirationDate);
  };

  const ScheduledOrInProgressSeriesOverlay = (
    <ScheduledSeriesOverlay
      cancelSeries={handleDeleteButton}
      openSeries={handleSelectedSeriesId}
    />
  );

  const RecordingOrFailedSeriesOverlay = (
    <RecordedSeriesOverlay
      deleteSeries={handleDeleteButton}
      openSeries={handleSelectedSeriesId}
    />
  );
  const SeriesOverlayByStatus: {
    [key in RecordingStatus]: React.ReactElement;
  } = {
    [RecordingStatus.SCHEDULED]: ScheduledOrInProgressSeriesOverlay,
    [RecordingStatus.IN_PROGRESS]: ScheduledOrInProgressSeriesOverlay,
    [RecordingStatus.RECORDED]: RecordingOrFailedSeriesOverlay,
    [RecordingStatus.FAILED]: RecordingOrFailedSeriesOverlay,
    [RecordingStatus.DELETED]: <></>,
  };

  const ScheduledOrInProgressRecordingOverlay = (
    <ScheduledRecordingOverlay
      cancelRecording={handleDeleteButton}
      openRecordingDetails={handleOpenRecordingDetails}
    />
  );

  const RecordingOverlayByStatus: {
    [key in RecordingStatus]: React.ReactElement;
  } = {
    [RecordingStatus.SCHEDULED]: ScheduledOrInProgressRecordingOverlay,
    [RecordingStatus.IN_PROGRESS]: ScheduledOrInProgressRecordingOverlay,
    [RecordingStatus.RECORDED]: (
      <RecordedRecordingOverlay
        withoutPlayButton={
          isRecordingExpired || isNpvrForRecordingChannelDisabled
        }
        deleteRecording={handleDeleteButton}
        openRecordingDetails={handleOpenRecordingDetails}
        onPlayClick={onPlayClick}
      />
    ),
    [RecordingStatus.FAILED]: (
      <FailedRecordingOverlay
        deleteRecording={handleDeleteButton}
        openRecordingDetails={handleOpenRecordingDetails}
      />
    ),
    [RecordingStatus.DELETED]: <></>,
  };

  return (
    <S.Container
      data-testid='RecordingPreview-Container'
      onMouseEnter={() => {
        setIsHover(true);
      }}
      onMouseLeave={() => {
        setIsHover(false);
      }}
    >
      <S.ImageWrapper $isSeries={isSeries} {...rest}>
        {status === RecordingStatus.IN_PROGRESS && <S.RecordingDot />}
        {status && isHover && !isBeforeDelete && (
          <S.ImageOverlay>
            {isSeries
              ? SeriesOverlayByStatus[status]
              : RecordingOverlayByStatus[status]}
          </S.ImageOverlay>
        )}
        {isBeforeDelete && (
          <S.ImageOverlay ref={beforeDeleteOverlay}>
            <DeleteConfirmation
              getDeleteQuestion={getDeleteQuestion}
              handleDeleteConfirmed={handleDeleteConfirmed}
            />
          </S.ImageOverlay>
        )}
        {!isHover && isRecordingFailed && !isBeforeDelete && (
          <S.ImageOverlay $withoutBorder>
            <IconWarning color={appTheme.colors.red} scale={0.5} />
          </S.ImageOverlay>
        )}
        {src && <Image src={src} />}
      </S.ImageWrapper>
      <RecordingPreviewCaption
        title={title}
        isSeries={isSeries}
        dateOfEmission={dateOfEmission}
        shouldShowRemainingTime={shouldShowRemainingTime}
        remainingTime={remainingTime}
        isRecordingExpired={isRecordingExpired}
        isRecordingFailed={isRecordingFailed}
        isNpvrForRecordingChannelDisabled={isNpvrForRecordingChannelDisabled}
      />
    </S.Container>
  );
};
