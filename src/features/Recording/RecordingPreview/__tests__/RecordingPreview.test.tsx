import { screen } from '@testing-library/react';
import { render } from 'utils/testing';
import { RecordingPreview } from '../RecordingPreview';
import { vi } from 'vitest';
import { setupMockServer } from 'services/api/mock/mock.server';
import { RecordingStatus } from 'services/api/newApi/optional/npvr';

describe('Components:RecordingPreview', () => {
  setupMockServer();
  const mockedDate = new Date(2022, 1, 22);
  vi.setSystemTime(mockedDate);

  it('should render RecordingPreview component', () => {
    render(
      <RecordingPreview
        recordingExtId={'test-id'}
        channelExtId={'test-channel-id'}
        src='https://www.fillmurray.com/240/300'
        title='Test'
        status={RecordingStatus.RECORDED}
      />,
    );
    expect(screen.getByText('Test')).toBeInTheDocument();
  });

  it('should render RecordingPreview component with EmissionDate and RemainingTime for today', async () => {
    const { getByTestId, findByText } = render(
      <RecordingPreview
        recordingExtId={'test-id'}
        channelExtId={'test-channel-id'}
        src='https://www.fillmurray.com/240/300'
        title='Test'
        dateOfEmission='22.02.2022'
        remainingTime='2h 20min'
        status={RecordingStatus.RECORDED}
      />,
    );
    expect(getByTestId('emission-date')).toHaveTextContent('22.02.2022');

    expect(
      await findByText((content: string) =>
        content.startsWith('Dostępny do dzisiaj'),
      ),
    ).toBeInTheDocument();
  });

  it('should render RecordingPreview component with EmissionDate and RemainingTime more than 24 hours', () => {
    const { getByTestId } = render(
      <RecordingPreview
        recordingExtId={'test-id'}
        channelExtId={'test-channel-id'}
        src='https://www.fillmurray.com/240/300'
        title='Test'
        dateOfEmission='22.02.2022'
        remainingTime='36h 20min'
        status={RecordingStatus.RECORDED}
      />,
    );
    expect(getByTestId('emission-date')).toHaveTextContent('22.02.2022');

    const remainingTimeElement = getByTestId('remaining-time');
    expect(remainingTimeElement).toHaveTextContent('Dostępny do jutra 12:20');
  });
});
