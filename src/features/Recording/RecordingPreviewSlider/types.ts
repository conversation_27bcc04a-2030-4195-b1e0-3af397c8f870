import { Dispatch, SetStateAction } from 'react';

import { SliderOptionsProps } from 'components/Slider';
import { RecordingSeriesParams } from 'services/api/newApi/optional/npvr';

import { NormalizedRecording } from '../types';

export interface RecordingPreviewSliderProps extends SliderOptionsProps {
  title?: string;
  data: Array<NormalizedRecording>;
  isFetching: boolean;
  slidesGap?: number;
  slidesInViewport?: number;
  renderOptions?: () => JSX.Element;
  setGridView?: Dispatch<SetStateAction<boolean>>;
  setSelectedSeriesParams?: Dispatch<
    SetStateAction<RecordingSeriesParams | undefined>
  >;
  isScheduled?: boolean;
  isVisible?: boolean;
}

export interface useRecordingPreviewSliderValue {
  data: Array<NormalizedRecording>;
  setGridView?: Dispatch<SetStateAction<boolean>>;
  isScheduled: boolean;
}
