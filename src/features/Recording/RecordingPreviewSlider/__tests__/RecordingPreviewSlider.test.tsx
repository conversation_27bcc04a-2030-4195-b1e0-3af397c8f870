import { screen } from '@testing-library/react';
import { render } from 'utils/testing';
import { NormalizedRecording } from 'features/Recording/types';

import { RecordingPreviewSlider } from '../RecordingPreviewSlider';

describe('Components:RecordingPreviewSlider', () => {
  it('should render RecordingPreviewSlider component', () => {
    render(
      <RecordingPreviewSlider
        data={
          [
            {
              name: 'RECORDING',
              imageSrc: 'https://www.fillmurray.com/355/200',
              isSeries: false,
            },
          ] as NormalizedRecording[]
        }
        isFetching={false}
      />,
    );
    expect(screen.getByText('RECORDING')).toBeInTheDocument();
  });

  it('should render all elements inside RecordingPreviewSlider', () => {
    const sliderData = [
      {
        name: 'RECORDING1',
        imageSrc: 'https://www.fillmurray.com/355/200',
        isSeries: false,
      },
      {
        name: 'RECORDING2',
        imageSrc: 'https://www.fillmurray.com/355/200',
        isSeries: false,
      },
      {
        name: 'RECORDING3',
        imageSrc: 'https://www.fillmurray.com/355/200',
        isSeries: false,
      },
    ];
    const { container } = render(
      <RecordingPreviewSlider
        title={'test'}
        data={sliderData as NormalizedRecording[]}
        isFetching={false}
      />,
    );
    const renderedImg = container.querySelectorAll('img');
    renderedImg.forEach((img, i) =>
      expect(img).toHaveAttribute('src', sliderData[i].imageSrc),
    );
  });
});
