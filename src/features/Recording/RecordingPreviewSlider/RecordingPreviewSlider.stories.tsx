import { Story, Meta } from '@storybook/react';

import { RecordingPreviewSlider } from './RecordingPreviewSlider';
import { NormalizedRecording } from '../types';

export default {
  title: 'Components/RecordingPreviewSlider',
  component: RecordingPreviewSlider,
} as Meta;

const data: NormalizedRecording[] = [];

for (let i = 0; i < 6; i++) {
  data.push({
    name: `${i % 2 === 0 ? 'Default Title' : 'Another Title'}`,
    imageSrc: 'https://picsum.photos/355/200',
    isSeries: false,
  } as NormalizedRecording);
}

const Template: Story = ({ title, isFetching, setGridView }) => (
  <RecordingPreviewSlider
    title={title}
    data={data}
    isFetching={isFetching}
    setGridView={setGridView}
  />
);

export const Default = Template.bind({});
Default.args = {
  title: 'RecordingPreviewSlider Title',
  isFetching: false,
  setGridView: false,
};
