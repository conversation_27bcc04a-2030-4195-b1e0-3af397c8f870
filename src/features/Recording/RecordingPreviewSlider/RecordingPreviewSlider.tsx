import { useMemo } from 'react';

import { IconButton } from 'components/Buttons/IconButton';
import { Select } from 'components/Forms/Select';
import { IconGrid } from 'components/Icons';
import { Loader } from 'components/Loader';
import { SliderWithControl } from 'components/SliderWithControl';
import { H2 } from 'components/Typography';
import { RecordingPreview } from 'features/Recording/RecordingPreview';
import { formatTimestampDate, getTimeRemainingUntilNow } from 'utils/dateUtils';
import { RecordingStatus } from 'services/api/newApi/optional/npvr';

import { useRecordingPreviewSlider } from './hooks';
import { RecordingPreviewSliderProps } from './types';
import * as S from './styles';

import { RECORDING_SORTING_OPTIONS } from '../RecordingSortOrderContext/constants';

export const RecordingPreviewSlider = ({
  title,
  data,
  isFetching,
  slidesGap = 16,
  slidesInViewport = 1,
  setGridView,
  setSelectedSeriesParams,
  isScheduled = false,
  isVisible = true,
  ...rest
}: RecordingPreviewSliderProps) => {
  const {
    sortingOption,
    sortedRecordings,
    setIsSliderHover,
    isSliderHover,
    filterSlides,
    goToGridView,
  } = useRecordingPreviewSlider({ data, setGridView, isScheduled });

  const recordings = useMemo(() => {
    return sortedRecordings?.map((recording, index) => {
      const {
        recordingExtId,
        recordingSeriesId,
        channelExtId,
        name,
        imageSrc,
        startDateEpg,
        expirationDate,
        recordingSeason,
        status,
        isSeries,
        prLevel,
      } = recording;

      return (
        <S.Poster key={index}>
          <RecordingPreview
            recordingExtId={recordingExtId}
            recordingSeriesId={recordingSeriesId}
            channelExtId={channelExtId}
            title={name || ''}
            src={imageSrc}
            dateOfEmission={formatTimestampDate(startDateEpg || 0)}
            remainingTime={getTimeRemainingUntilNow(expirationDate || 0)}
            expirationDate={formatTimestampDate(expirationDate || 0)}
            status={status || RecordingStatus.RECORDED}
            isSeries={isSeries}
            recordingSeason={recordingSeason}
            prLevel={prLevel}
            setSelectedSeriesParams={() => {
              const areSeriesParamsAvailable =
                status && recordingSeriesId && channelExtId && recordingSeason;

              if (areSeriesParamsAvailable && setSelectedSeriesParams) {
                setSelectedSeriesParams({
                  recordingStatus: status,
                  recordingSeriesId: recordingSeriesId,
                  channelExtId: channelExtId,
                  recordingSeason: recordingSeason,
                });
              }
            }}
          />
        </S.Poster>
      );
    });
  }, [setSelectedSeriesParams, sortedRecordings]);

  return isFetching || sortedRecordings?.length > 0 ? (
    <S.Container
      data-testid='RecordingPreviewSlider-Slider'
      onMouseEnter={() => setIsSliderHover(true)}
      onMouseLeave={() => setIsSliderHover(false)}
      $isVisible={isVisible}
    >
      {!isFetching ? (
        <SliderWithControl
          renderOptions={() => {
            return (
              <S.SliderHeader>
                {Boolean(title) && <H2>{title}</H2>}
                <S.OptionsWrapper $isSliderHover={isSliderHover}>
                  <S.SelectWrapper>
                    <Select
                      id='recordings-sort'
                      options={
                        isScheduled
                          ? RECORDING_SORTING_OPTIONS.SCHEDULED
                          : RECORDING_SORTING_OPTIONS.RECORDED
                      }
                      onChange={filterSlides}
                      value={{
                        label: sortingOption?.label,
                        value: String(sortingOption?.value),
                      }}
                    />
                  </S.SelectWrapper>
                  <S.IconWrapper>
                    <IconButton onClick={goToGridView}>
                      <IconGrid />
                    </IconButton>
                  </S.IconWrapper>
                </S.OptionsWrapper>
              </S.SliderHeader>
            );
          }}
          {...rest}
        >
          {recordings}
        </SliderWithControl>
      ) : (
        <Loader />
      )}
    </S.Container>
  ) : (
    <></>
  );
};
