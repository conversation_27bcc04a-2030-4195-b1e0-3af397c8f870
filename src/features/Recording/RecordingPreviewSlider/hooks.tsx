import { useCallback, useEffect, useMemo, useState } from 'react';

import {
  SortDirection,
  SortingOption,
  useSortedData,
} from 'hooks/useSortedData';
import { useScrollLock } from 'hooks/useScrollLock';
import { useDevice } from 'services/device';

import { useRecordingPreviewSliderValue } from './types';

import { prepareRecordingsForSorting } from '../recordingSortUtils';
import { useRecordingSortOrder } from '../RecordingSortOrderContext';
import { RECORDINGS_DEFAULT_SORT_KEY } from '../constants';
import { NormalizedRecording } from '../types';

export const useRecordingPreviewSlider = (
  props: useRecordingPreviewSliderValue,
) => {
  const { data: recordings, setGridView, isScheduled } = props;
  const { isMobileDevice } = useDevice();
  const { lockScroll } = useScrollLock();

  const [isSliderHover, setIsSliderHover] = useState(isMobileDevice);
  const { sortedData: sortedRecordings, setNewSortedData } =
    useSortedData<NormalizedRecording>(
      recordings,
      RECORDINGS_DEFAULT_SORT_KEY,
      SortDirection.Descending,
    );

  const {
    activeScheduledSortOption,
    activeRecordedSortOption,
    handleActiveRecordedSortOptionChange,
    handleActiveScheduledSortOptionChange,
  } = useRecordingSortOrder();

  const sortingOption = useMemo(
    () => (isScheduled ? activeScheduledSortOption : activeRecordedSortOption),
    [activeRecordedSortOption, activeScheduledSortOption, isScheduled],
  );

  useEffect(() => {
    const preparedRecordings = prepareRecordingsForSorting(
      recordings,
      sortingOption,
    );
    setNewSortedData(preparedRecordings, sortingOption);
  }, [recordings, setNewSortedData, sortingOption]);

  const filterSlides = (option: SortingOption) => {
    isScheduled
      ? handleActiveScheduledSortOptionChange(option)
      : handleActiveRecordedSortOptionChange(option);
    const preparedRecordings = prepareRecordingsForSorting(recordings, option);
    setNewSortedData(preparedRecordings, option);
  };

  const goToGridView = useCallback(() => {
    lockScroll();
    setGridView?.(true);
  }, [setGridView, lockScroll]);

  return {
    sortingOption,
    sortedRecordings,
    setIsSliderHover,
    isSliderHover,
    filterSlides,
    goToGridView,
  };
};
