import { useCallback, useEffect, useMemo, useState } from 'react';
import { useIntl } from 'react-intl';

import { usePlayContent } from 'features/Player/Hook';
import { initialDataDetailsView } from 'services/detailsView/constants';
import { formatTimestampDate } from 'utils/dateUtils';
import { getPublicAssetUrl } from 'utils/url';
import { usePlayerPlayback } from 'features/Player/Context';
import { useChannels } from 'routes/Channels/hooks';
import {
  useRecordingDeleteMutation,
  useRecordingDetailsQuery,
} from 'services/api/newApi/optional/npvr';

import { messages } from './messages';
import { DetailsRecordingProps } from './types';

export const useDetailsRecording = ({
  dataDetailsView,
  setBackgroundImage,
  setData,
}: DetailsRecordingProps) => {
  const { data: recording, isFetching } = useRecordingDetailsQuery({
    recordingExtId: dataDetailsView.id,
  });
  const { allChannels } = useChannels();

  const { playRecording } = usePlayContent();
  const { formatMessage } = useIntl();
  const { mutateAsync } = useRecordingDeleteMutation();
  const { reset: resetPlayback } = usePlayerPlayback();

  const [image, setImage] = useState('');
  const [isBeforeDelete, setIsBeforeDelete] = useState(false);

  const dateOfEmission = recording?.startDateEpg
    ? formatTimestampDate(recording.startDateEpg)
    : dataDetailsView.dateEmission;
  const remainingTime = recording?.expirationDate
    ? formatTimestampDate(recording?.expirationDate)
    : dataDetailsView.expirationDate;
  const countries = recording?.countries && recording.countries.join(', ');
  const isRecording =
    recording?.status === 'scheduled' || recording?.status === 'inProgress';
  const isFailed = recording?.status === 'failed';
  const description = recording?.description;
  const recordingChannelName = useMemo(
    () =>
      allChannels.find(
        (channel) => channel.channelExtId === recording?.channelExtId,
      )?.name,
    [allChannels, recording?.channelExtId],
  );

  const getEpisodeName = useCallback(() => {
    if (recording?.episodeName) {
      return `(${recording.episodeName})`;
    }
    if (recording?.originalEpisodeName) {
      return `(${recording.originalEpisodeName})`;
    }
    return '';
  }, [recording?.episodeName, recording?.originalEpisodeName]);

  const shortInfo = useMemo(() => {
    const shortInfoArray = [
      { title: formatMessage(messages.emissionDate), info: dateOfEmission },
      {
        title: formatMessage(messages.remainingTime),
        info: remainingTime,
      },
      { title: formatMessage(messages.channel), info: recordingChannelName },
      { title: formatMessage(messages.production), info: countries },
      {
        title: formatMessage(messages.episode),
        info: `${recording?.episodeNumber}${
          getEpisodeName() ? ' ' : ''
        }${getEpisodeName()}`,
      },
    ];

    if (recording?.recordingSeason !== '0') {
      shortInfoArray.push({
        title: formatMessage(messages.season),
        info: recording?.recordingSeason,
      });
    }

    return shortInfoArray;
  }, [
    countries,
    dateOfEmission,
    formatMessage,
    getEpisodeName,
    recording?.episodeNumber,
    recording?.recordingSeason,
    recordingChannelName,
    remainingTime,
  ]);

  useEffect(() => {
    const imagePath = recording?.imagePaths?.small;
    const recordingImage = recording?.image;
    if (imagePath && recordingImage) {
      const imageUrl = getPublicAssetUrl(`${imagePath}${recordingImage}`);
      setImage(imageUrl);
      setBackgroundImage(imageUrl);
    }
  }, [recording?.image, recording?.imagePaths?.small, setBackgroundImage]);

  const handleDeleteButton = () => {
    setIsBeforeDelete(true);
  };

  const handleDeniedButton = () => {
    setIsBeforeDelete(false);
  };

  const handleDeleteConfirmed = useCallback(async () => {
    setIsBeforeDelete(false);
    if (recording?.recordingExtId && recording?.status) {
      await mutateAsync({
        recordingExtId: recording.recordingExtId,
        status: recording?.status,
      });
      setData(initialDataDetailsView);
      resetPlayback();
    }
  }, [mutateAsync, recording, resetPlayback, setData]);

  const onPlayClick = () => {
    if (recording?.channelExtId && recording.recordingExtId) {
      playRecording({
        channelExtId: recording.channelExtId,
        recordingExtId: recording.recordingExtId,
        programParentalControlLevel: recording.prLevel,
      });
      setData(initialDataDetailsView);
    }
  };

  return {
    isFetching,
    isRecording,
    title: recording?.name,
    prLevel: recording?.prLevel,
    description,
    image,
    shortInfo,
    isBeforeDelete,
    isFailed,
    recordingchannelExtId: recording?.channelExtId || '',
    onPlayClick,
    handleDeleteButton,
    handleDeniedButton,
    handleDeleteConfirmed,
  };
};
