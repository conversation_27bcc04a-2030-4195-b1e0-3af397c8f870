import { useCallback, useEffect, useRef } from 'react';
import { toast } from 'react-toastify';

import { useAuthenticationStatus } from 'services/user';
import { minutesToMilliseconds } from 'utils/time';
import { useConfig } from 'services/config';

import { RandomFeedbackToast } from '../components/RandomFeedbackToast';
import {
  MAX_TIME_IN_MINUTES,
  MIN_TIME_IN_MINUTES,
  SCHEDULED_TOAST_TIME_LOCAL_STORAGE_KEY,
  UPDATE_SCHEDULED_TIME_INTERVAL,
} from '../constants';

export const useRandomFeedbackToast = (
  defaultMinTimeInMinutes = MIN_TIME_IN_MINUTES,
  defaultMaxTimeInMinutes = MAX_TIME_IN_MINUTES,
) => {
  const { isAuthenticated } = useAuthenticationStatus();
  const { rating: ratingConfig } = useConfig().config.appConfig || {};

  const showToast = ratingConfig?.showToast;

  const minTimeInMinutes = ratingConfig
    ? ratingConfig.randomMinTimeInMinutes
    : defaultMinTimeInMinutes;
  const maxTimeInMinutes = ratingConfig
    ? ratingConfig.randomMaxTimeInMinutes
    : defaultMaxTimeInMinutes;

  const timeoutId = useRef<NodeJS.Timeout>();

  const showRandomFeedbackToast = () => {
    toast(<RandomFeedbackToast />, {
      type: 'info',
      position: 'top-right',
      hideProgressBar: false,
      autoClose: 10000,
    });
  };

  const getRandomTime = useCallback(() => {
    return (
      Math.random() *
        (minutesToMilliseconds(maxTimeInMinutes) -
          minutesToMilliseconds(minTimeInMinutes)) +
      minutesToMilliseconds(minTimeInMinutes)
    );
  }, [maxTimeInMinutes, minTimeInMinutes]);

  const scheduleNewToast = useCallback(
    (now: number) => {
      const newScheduledTime = Math.floor(now + getRandomTime());
      localStorage.setItem(
        SCHEDULED_TOAST_TIME_LOCAL_STORAGE_KEY,
        String(newScheduledTime),
      );
    },
    [getRandomTime],
  );

  const updateScheduledTime = useCallback(() => {
    const now = new Date().getTime();
    const scheduledToastTimeString = localStorage.getItem(
      SCHEDULED_TOAST_TIME_LOCAL_STORAGE_KEY,
    );

    if (scheduledToastTimeString) {
      const scheduledToastTime = Number(scheduledToastTimeString);
      const remainingTime = scheduledToastTime - now;

      if (remainingTime <= 0) {
        scheduleNewToast(now);
      }
    }
  }, [scheduleNewToast]);

  const generateRandomToast = useCallback(() => {
    const now = new Date().getTime();
    const scheduledToastTimeString = localStorage.getItem(
      SCHEDULED_TOAST_TIME_LOCAL_STORAGE_KEY,
    );

    if (!scheduledToastTimeString) {
      return scheduleNewToast(now);
    }

    const scheduledToastTime = Number(scheduledToastTimeString);
    const remainingTime = scheduledToastTime - now;

    if (remainingTime <= 0) {
      showRandomFeedbackToast();
      return scheduleNewToast(now);
    }

    timeoutId.current = setTimeout(() => {
      showRandomFeedbackToast();
      scheduleNewToast(now);
    }, remainingTime);
  }, [scheduleNewToast]);

  useEffect(() => {
    const intervalId = setInterval(
      updateScheduledTime,
      UPDATE_SCHEDULED_TIME_INTERVAL,
    );

    if (isAuthenticated && showToast) {
      generateRandomToast();
    }

    return () => {
      if (timeoutId.current) {
        clearTimeout(timeoutId.current);
      }
      clearInterval(intervalId);
    };
  }, [
    generateRandomToast,
    isAuthenticated,
    scheduleNewToast,
    showToast,
    updateScheduledTime,
  ]);
};
