import { customRenderWithLoader } from 'utils/testing';
import { baseAssertions } from 'utils/testing/baseAssertions';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { PinUpdateProcess } from '../PinUpdateProcess';
import { setupMockServer } from 'services/api/mock/mock.server';
import { pinRemainingAttemptsMockHandlers } from 'services/api/newApi/core/household/mocks/pinRemainingAttempts.mock';
import { vi } from 'vitest';

describe('PinUpdateProcess', () => {
  setupMockServer(...pinRemainingAttemptsMockHandlers);
  const mockFn = vi.fn();

  beforeEach(async () => {
    const { findByText } = customRenderWithLoader(
      <PinUpdateProcess stopProcess={mockFn} />,
    );
    await baseAssertions.isAppLoaded();
    await findByText('<PERSON><PERSON><PERSON> kod dostępu');
  });

  it('should call onCloseCallback after close button click', async () => {
    const closeButton = await screen.findByTestId('IconCloseSmall');
    await userEvent.click(closeButton);
    expect(mockFn).toHaveBeenCalledTimes(1);
  });

  it('should enable send button when form is filled correctly', async () => {
    const sendButton = await screen.findByRole('button', { name: 'Dalej' });
    await waitFor(() => {
      expect(sendButton).toBeDisabled();
    });

    const currentPinInput = screen.getByPlaceholderText('Aktualny kod dostępu');
    await userEvent.type(currentPinInput, '1111');

    const newPinInput = screen.getByPlaceholderText('Nowy kod dostępu');
    await userEvent.type(newPinInput, '1111');

    const newPinConfirmationInput = screen.getByPlaceholderText(
      'Powtórz nowy kod dostępu',
    );
    await userEvent.type(newPinConfirmationInput, '1111');

    await waitFor(() => {
      expect(sendButton).toBeEnabled();
    });
  });

  it('should display error if pins are not equal', async () => {
    const newPinInput = screen.getByPlaceholderText('Nowy kod dostępu');
    await userEvent.type(newPinInput, '4321');

    const newPinConfirmationInput = screen.getByPlaceholderText(
      'Powtórz nowy kod dostępu',
    );
    await userEvent.type(newPinConfirmationInput, '1234');
    await screen.findByText(/Wprowadzone kody nie są zgodne/);
  });

  it('should display error if new pin is 0000', async () => {
    const newPinInput = screen.getByPlaceholderText('Nowy kod dostępu');
    await userEvent.type(newPinInput, '0000');
    await screen.findByText(/Kod musi być różny od 0000/);
  });

  it('should change authorization method after click button', async () => {
    await screen.findByText(
      /Aby zmienić kod dostępu, wprowadź aktualny kod dostępu do usługi./,
    );
    const changeAuthorizationMethodButton = await screen.findByRole('button', {
      name: 'Kod poufny',
    });
    userEvent.click(changeAuthorizationMethodButton);
    await waitFor(() => {
      screen.findByText(
        /Aby zmienić kod dostępu, wprowadź aktualny kod poufny/,
      );
    });
  });
});
