import { customRenderWithLoader } from 'utils/testing';
import { act, screen, waitFor } from '@testing-library/react';

import { DiagnosticsCenter } from '../DiagnosticsCenter';
import { setupMockServer } from 'services/api/mock/mock.server';
import { baseAssertions } from 'utils/testing/baseAssertions';
import userEvent from '@testing-library/user-event';

import {
  getPmsTokenErrorWhenTerminalNotExistMockHandler,
  getPmsTokenMockHandler,
} from 'services/api/newApi/core/device/mocks/pmsToken.mock';
import {
  addDeviceWithErrorMockHandlers,
  getDevicesMaxMockHandlers,
} from 'services/api/newApi/core/device/mocks/devicesHandlers.mock';
import { vi } from 'vitest';
import * as postDiagnosticTestReport from 'services/api/pmsApi';

describe('DiagnosticsCenter', () => {
  const server = setupMockServer();

  beforeEach(async () => {
    const { findByText } = customRenderWithLoader(<DiagnosticsCenter />);
    await baseAssertions.isAppLoaded();
    const sectionHeader = await findByText('Centrum diagnostyki');
    await userEvent.click(sectionHeader);
  });

  it('should display enabled diagnose service button', async () => {
    const button = screen.getByRole('button', { name: 'Diagnozuj usługę' });
    expect(button).toBeEnabled();
  });

  it('should display disabled diagnose service button after diagnostic', async () => {
    const button = screen.getByRole('button', { name: 'Diagnozuj usługę' });
    userEvent.click(button);
    await waitFor(() => {
      expect(button).toBeDisabled();
    });
  });

  it('should display add new device in case of invalid terminal as response for get pms token and close on cancel button', async () => {
    server.use(...getPmsTokenErrorWhenTerminalNotExistMockHandler);
    const diagnosticButton = screen.getByRole('button', {
      name: 'Diagnozuj usługę',
    });
    userEvent.click(diagnosticButton);
    await waitFor(() => {
      expect(screen.getByText('Brak urządzenia')).toBeInTheDocument();
    });
    const cancelButton = screen.getByRole('button', { name: 'Anuluj' });
    userEvent.click(cancelButton);
    await waitFor(() => {
      expect(screen.queryByText('Brak urządzenia')).not.toBeInTheDocument();
    });
  });

  it('should display delete terminal modal on error', async () => {
    server.use(
      ...getPmsTokenErrorWhenTerminalNotExistMockHandler,
      ...addDeviceWithErrorMockHandlers,
      ...getDevicesMaxMockHandlers,
    );
    const diagnosticButton = screen.getByRole('button', {
      name: 'Diagnozuj usługę',
    });
    userEvent.click(diagnosticButton);
    await waitFor(() => {
      expect(screen.getByText('Brak urządzenia')).toBeInTheDocument();
    });
    const submitButton = screen.getByRole('button', {
      name: 'Dodaj urządzenie',
    });
    userEvent.click(submitButton);
    await waitFor(() => {
      expect(
        screen.getByText('Przekroczono limit urządzeń'),
      ).toBeInTheDocument();
    });
  });

  it('should call post report to pms after diagnostic', async () => {
    server.use(...getPmsTokenMockHandler);
    const spy = vi.spyOn(postDiagnosticTestReport, 'postDiagnosticTestReport');
    const button = screen.getByRole('button', { name: 'Diagnozuj usługę' });
    userEvent.click(button);
    await waitFor(() => {
      expect(button).toBeDisabled();
      expect(spy).toHaveBeenCalledTimes(1);
    });
  });
});
