import { useMemo, useState } from 'react';
import { useIntl } from 'react-intl';
import { format } from 'date-fns/format';

import {
  useCoreHouseholdQuery,
  useHouseholdTveServiceQuery,
} from 'services/api/newApi/core/household';
import {
  DAY_MONTH_YEAR_PATTERN,
  MILLISECONDS_IN_SECOND,
} from 'utils/dateUtils/constants';
import { useAuthenticationStatus } from 'services/user';
import { useConfig } from 'services/config';

import { useAnalyticsAgreement } from './AnalyticsAgreement';

export const useService = () => {
  const { formatMessage } = useIntl();
  const { analyticsAgreement, setAnalyticsAgreement } = useAnalyticsAgreement();
  const { config, getTechConfig } = useConfig();
  const { appConfig } = config;
  const { isAuthenticated } = useAuthenticationStatus();

  const shouldShowWatchOnBigScreenButton = Boolean(
    getTechConfig()?.infoScreen?.isVisibleToUser,
  );
  const [isAtvAppPopupVisible, setIsAtvPopupVisible] = useState(false);

  const { data: householdInfo } = useCoreHouseholdQuery(isAuthenticated);

  const shouldFetchTveData =
    isAuthenticated &&
    (appConfig?.techConfig.userInfo.isTveSubscriptionInfoVisible ?? true);

  const { data: householdTve } =
    useHouseholdTveServiceQuery(shouldFetchTveData);

  const householdExtId = householdInfo?.householdExtId;

  const getServiceBeginDate = () => {
    if (householdTve && householdTve?.activeFrom) {
      return format(
        new Date(householdTve.activeFrom * MILLISECONDS_IN_SECOND),
        DAY_MONTH_YEAR_PATTERN,
      );
    }
    return null;
  };

  const openAtvAppPopup = () => {
    setIsAtvPopupVisible(true);
  };

  const closeAtvAppPopup = () => {
    setIsAtvPopupVisible(false);
  };

  const getServiceEndDate = () => {
    if (householdTve && householdTve?.activeTo) {
      return format(
        new Date(householdTve.activeTo * MILLISECONDS_IN_SECOND),
        DAY_MONTH_YEAR_PATTERN,
      );
    }
    return null;
  };

  const isMultiRecorderActive = useMemo(() => {
    return Boolean(householdInfo?.npvr);
  }, [householdInfo?.npvr]);

  const handleCheckboxAnalyticsAgreement = () => {
    if (analyticsAgreement) {
      return setAnalyticsAgreement(false);
    }
    return setAnalyticsAgreement(true);
  };

  return {
    formatMessage,
    householdInfo,
    householdTve,
    handleCheckboxAnalyticsAgreement,
    getServiceBeginDate,
    analyticsAgreement,
    getServiceEndDate,
    householdExtId,
    isMultiRecorderActive,
    isTveSubscriptionInfoVisible:
      appConfig?.techConfig.userInfo.isTveSubscriptionInfoVisible,
    shouldShowWatchOnBigScreenButton,
    isAtvAppPopupVisible,
    openAtvAppPopup,
    closeAtvAppPopup,
  };
};
