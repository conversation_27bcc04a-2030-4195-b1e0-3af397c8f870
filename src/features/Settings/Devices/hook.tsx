import { useMemo, useState } from 'react';

import {
  useDeviceRegisterMutation,
  useDeviceUnregisterMutation,
  useRegisteredTerminalsQuery,
} from 'services/api/newApi/core/device/queries';
import { Terminal } from 'services/api/newApi/core/device/types';
import { useCoreHouseholdQuery } from 'services/api/newApi/core/household';
import { useConfig } from 'services/config';
import { generateTerminalSerialNumber, getBrowserType } from 'services/device';
import {
  isBadGatewayError,
  isHouseholdSuspendError,
  isMaxSlotsExceededError,
  useErrorScreen,
} from 'services/error';
import { useGlobalLoaderContext } from 'services/loader';
import { useAuthenticationStatus, useUserSession } from 'services/user';

import { TERMINALS_MAX_OTT_LIMIT } from './constants';

export const useDevice = () => {
  const { isAuthenticated } = useAuthenticationStatus();
  const { mutate: deleteMutate } = useDeviceUnregisterMutation();

  const { mutateAsync: addDeviceAsync } = useDeviceRegisterMutation();
  const { data: householdInfo } = useCoreHouseholdQuery(isAuthenticated);
  const { data: devices } = useRegisteredTerminalsQuery(isAuthenticated);
  const { logout } = useUserSession();

  const { setIsLoaderVisible } = useGlobalLoaderContext();
  const { showErrorModal } = useErrorScreen();

  const browserType = getBrowserType();

  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState<boolean>(false);
  const [currentSerialNumber, setCurrentSerialNumber] = useState<string>('');

  const {
    config: {
      terminal: {
        deviceType,
        deviceManufacture,
        deviceModel,
        serialNumberType,
      },
    },
  } = useConfig();

  const serialNumber = useMemo(() => {
    if (householdInfo) {
      const { householdExtId } = householdInfo;

      return generateTerminalSerialNumber(
        browserType,
        serialNumberType,
        householdExtId,
      );
    }
    return '';
  }, [browserType, serialNumberType, householdInfo]);

  const numberOfActiveTerminals = useMemo(
    () => devices?.list?.length || 0,
    [devices?.list?.length],
  );

  const isDeviceAlreadyRegistered = Boolean(
    devices?.list?.find(
      (terminal: Terminal) => terminal.serialNumber === serialNumber,
    ),
  );

  const isDisabledAddButton =
    numberOfActiveTerminals >= TERMINALS_MAX_OTT_LIMIT ||
    isDeviceAlreadyRegistered;

  const registerTerminal = async () => {
    setIsLoaderVisible(true);

    if (!householdInfo) {
      return Promise.reject();
    }

    try {
      await addDeviceAsync({
        name: browserType,
        serialNumber,
        deviceType,
        deviceManufacture,
        deviceModel,
      });

      return Promise.resolve();
    } catch (error: unknown) {
      Promise.reject();
      if (isHouseholdSuspendError(error)) {
        logout();
        return showErrorModal('HOUSEHOLD_SUSPENDED_ON_ACTION');
      }
      if (isBadGatewayError(error)) {
        return showErrorModal('SYSTEM_FAILURE');
      }
      if (isMaxSlotsExceededError(error)) {
        return showErrorModal('UNABLE_TO_REGISTER_TERMINAL');
      }
      return showErrorModal('SYSTEM_FAILURE');
    } finally {
      setIsLoaderVisible(false);
    }
  };

  const deleteDevice = (terminalSerial: string) =>
    deleteMutate({
      serialNumber: terminalSerial,
    });

  return {
    isDeleteModalOpen,
    currentSerialNumber,
    disabledAddButton: isDisabledAddButton,
    setCurrentSerialNumber,
    setIsDeleteModalOpen,
    deleteDevice,
    registerTerminal,
    devices,
  };
};
