import { useCallback, useEffect, useState } from 'react';

import {
  Channel,
  useChannelsAllQuery,
} from 'services/api/newApi/live/channels';
import { useMyListQuery } from 'services/api/oldApi/myzone';
import { useRecentlyWatchedChannels } from 'services/localApi/RecentlyWatchedChannelsApiContext';

export const useWatchedChannels = () => {
  const {
    recentlyWatchedChannels = [],
    isFetching: isFetchingRecentlyWatched,
    isError: isRecentlyWatchedError,
    refetchRecentlyWatchedChannels,
  } = useRecentlyWatchedChannels();

  const {
    data: liveChannels = [],
    isFetching: isFetchingLiveChannels,
    isError: isLiveChannelsError,
    refetch: refetchLiveChannels,
  } = useChannelsAllQuery();

  const isFetching = isFetchingRecentlyWatched || isFetchingLiveChannels;
  const isError = isLiveChannelsError || isRecentlyWatchedError;

  const [isModalErrorOpen, setIsModalErrorOpen] = useState(false);

  useEffect(() => {
    if (isLiveChannelsError || isRecentlyWatchedError) {
      setIsModalErrorOpen(true);
    }
  }, [isLiveChannelsError, isRecentlyWatchedError]);

  const closeErrorModal = useCallback(() => {
    setIsModalErrorOpen(false);
  }, []);

  const refetchData = useCallback(() => {
    refetchLiveChannels();
    refetchRecentlyWatchedChannels();
  }, [refetchLiveChannels, refetchRecentlyWatchedChannels]);

  const extendedRecentlyWatchedChannels = recentlyWatchedChannels.map(
    (recentlyWatchedChannel) => {
      const extendedChannel = liveChannels?.find(
        (liveChannel) =>
          liveChannel.channelExtId === recentlyWatchedChannel.channelExtId,
      );
      return extendedChannel as Channel;
    },
  );

  return {
    watchedChannels: extendedRecentlyWatchedChannels,
    live: liveChannels,
    isFetching,
    isError,
    isModalErrorOpen,
    closeErrorModal,
    refetchData,
  };
};

export const useFavoritesChannels = () => {
  const { data: myList, isFetching: isMyListFetching } = useMyListQuery();
  const { data: liveChannels = [], isFetching: isFetchingLiveChannels } =
    useChannelsAllQuery();

  const isDataFetching = isMyListFetching || isFetchingLiveChannels;

  const favoritesChannels = myList?.channelList ?? [];

  const favoritesChannelsWithDetails = liveChannels.filter(
    (liveChannel) =>
      favoritesChannels?.some(
        (favoriteChannel) =>
          favoriteChannel.channelExternalId === liveChannel.channelExtId,
      ),
  );

  return { favoritesChannelsWithDetails, isDataFetching };
};
