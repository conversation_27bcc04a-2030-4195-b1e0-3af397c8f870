import { Loader } from 'components/Loader';
import { RecentlyWatched } from 'features/Home/components/RecentlyWatched';
import {
  RecentlyWatchedDataTypes,
  useRecentlyWatchedAllQuery,
} from 'services/api/oldApi/myzone';
import { useConfig } from 'services/config';
import { useAuthenticationStatus } from 'services/user';

import { useRecentlyWatchedCategory } from '../components/RecentlyWatched/hooks';

export const ContinueWatching = () => {
  const { isAuthenticated } = useAuthenticationStatus();

  const { getTechConfig } = useConfig();
  const {
    myZone: { withContinueWatching },
  } = getTechConfig();

  const {
    data: allRecentlyWatched = {
      programList: [],
      recordingList: [],
      vodList: [],
    },
    isFetching: isFetchingAllRecentlyWatched,
  } = useRecentlyWatchedAllQuery({
    dataTypes: [
      RecentlyWatchedDataTypes.Programs,
      RecentlyWatchedDataTypes.Recordings,
      RecentlyWatchedDataTypes.Vods,
    ],
  });
  const { programList, recordingList, vodList } = allRecentlyWatched;

  const { activeCategory, setActiveCategory } = useRecentlyWatchedCategory(
    programList,
    recordingList,
    vodList,
  );

  if (isFetchingAllRecentlyWatched) {
    return <Loader />;
  }

  if (isAuthenticated && withContinueWatching) {
    return (
      <RecentlyWatched
        programs={programList}
        recordings={recordingList}
        vod={vodList}
        activeCategory={activeCategory}
        handleCategoryChange={setActiveCategory}
      />
    );
  }

  return null;
};
