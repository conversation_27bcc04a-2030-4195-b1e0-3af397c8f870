import { useIntl } from 'react-intl';

import { ChannelsGrid } from 'components/ChannelsGrid';
import { Loader } from 'components/Loader';
import { messages } from 'features/Home/messages';
import { ModalConfirmation } from 'components/ModalConfirmation';
import { useAuthenticationStatus } from 'services/user';
import { useConfig } from 'services/config';

import { useWatchedChannels } from './hooks';

export const WatchedChannels = () => {
  const { formatMessage } = useIntl();
  const { isAuthenticated } = useAuthenticationStatus();
  const { withRecentlyWatchedChannels } = useConfig().getTechConfig().myZone;

  const {
    watchedChannels,
    isFetching,
    isError,
    refetchData,
    isModalErrorOpen,
    closeErrorModal,
  } = useWatchedChannels();

  if (isFetching) {
    return <Loader />;
  }

  if (isError) {
    return (
      <ModalConfirmation
        modalTitle={formatMessage(messages.modalError.modalTitle)}
        isOpen={isModalErrorOpen}
        onClose={closeErrorModal}
        onDenied={closeErrorModal}
        onSubmit={refetchData}
        buttonSubmitText={formatMessage(messages.modalError.modalConfirmation)}
        buttonDeniedText={formatMessage(messages.modalError.modalDenied)}
      />
    );
  }
  if (isAuthenticated && withRecentlyWatchedChannels) {
    return (
      <ChannelsGrid
        channels={watchedChannels}
        isFetching={isFetching}
        title={formatMessage(messages.recentlyWatchedChannels)}
        isSlider={true}
        amountLines={1}
        noDataMessage={formatMessage(messages.noRecentlyWatchedChannels)}
      />
    );
  }

  return null;
};
