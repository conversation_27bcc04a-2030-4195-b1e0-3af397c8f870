import styled, { css } from 'styled-components';

import {
  convertHexToRgbaString,
  devices,
  RenderLayer,
  setSliderRecordingCardSizes,
} from 'theme';

export const ClosedSearchWrapper = styled.div`
  margin-right: 3.2rem;
`;

export const Container = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: center;
  background: ${({ theme }) => convertHexToRgbaString(theme.colors.black, 0.8)};

  ${RenderLayer('appOverlay')};
`;

export const ResultsContainer = styled.div`
  position: fixed;
  left: 0;
  right: 0;
  top: 6.4rem;
  width: 100vw;
  height: calc(100vh - 6.4rem);
  overflow: scroll;
  padding: 3.2rem 0 0 4.8rem;
`;

export const SearchButtonWrapper = styled.div`
  transform: translateX(100%);
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const CloseButtonWrapper = styled.div`
  transform: translateX(-100%);
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const PosterContainer = css`
  flex-grow: 0;
  flex-shrink: 0;
  margin-right: 1.6rem;
`;

export const ChannelPosterContainer = styled.div`
  display: flex;
  flex-shrink: 0;
  margin-right: 3.6rem;
  > div {
    max-width: 18rem;
    height: auto;
    justify-content: space-between;

    div {
      min-height: auto;
      height: auto;
    }
  }
`;

export const LivePosterContainer = styled.div`
  max-width: 35.5rem;
  max-height: 25rem;
  ${PosterContainer};
  ${setSliderRecordingCardSizes('1.6rem')};
`;

export const VodPosterContainer = styled.div`
  ${PosterContainer};
  flex-basis: calc(100% / 1.8 - 1.6rem);
  min-width: 24rem;
  max-width: 24rem;

  &:hover img {
    filter: brightness(50%);
  }

  @media ${devices.mobileS} {
    flex-basis: calc(100% / 2.8 - 1.6rem);
  }
  @media ${devices.tabletS} {
    flex-basis: calc(100% / 3.2 - 1.6rem);
  }
  @media ${devices.desktopS} {
    flex-basis: calc(100% / 7.3 - 1.6rem);
  }
`;

export const SliderWrapper = styled.div`
  margin-bottom: 3.2rem;

  > div {
    margin: 0;
  }
`;

export const NoResultsContainer = styled.div`
  position: absolute;
  left: 0;
  right: 0;
  text-align: center;
`;

ClosedSearchWrapper.displayName = 'SearchClosedSearchWrapper';
Container.displayName = 'SearchContainer';
ResultsContainer.displayName = 'SearchResultsContainer';
SearchButtonWrapper.displayName = 'SearchSearchButtonWrapper';
CloseButtonWrapper.displayName = 'SearchCloseButtonWrapper';
LivePosterContainer.displayName = 'SearchLivePosterContainer';
VodPosterContainer.displayName = 'SearchVodPosterContainer';
SliderWrapper.displayName = 'SearchSliderWrapper';
NoResultsContainer.displayName = 'SearchNoResultsContainer';
