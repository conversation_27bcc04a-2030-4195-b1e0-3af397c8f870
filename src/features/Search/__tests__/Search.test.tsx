import {
  act,
  fireEvent,
  getAllByTestId,
  getAllByText,
  waitFor,
} from '@testing-library/react';
import { render } from 'utils/testing';

import { DetailsOverlayWrapper } from 'services/detailsView';
import { setupMockServer } from 'services/api/mock/mock.server';

import {
  epgExtendedMockHandlers,
  epgMockHandlers,
  searchDataMockHandlers,
  vodAssetMockHandlers,
  vodPurchasesAssetMockHandlers,
  channelsAllMockHandlers,
  regionalListMockHandlers,
  appConfigsMockHandlers,
} from 'services/api/mock/mocksIndex';

import { Search } from '../Search';

describe('Components:RecordingPreviewSlider', () => {
  setupMockServer(
    ...appConfigsMockHandlers,
    ...searchDataMockHandlers,
    ...epgMockHandlers,
    ...epgExtendedMockHandlers,
    ...vodAssetMockHandlers,
    ...vodPurchasesAssetMockHandlers,
    ...channelsAllMockHandlers,
    ...regionalListMockHandlers,
  );

  const setupSearch = () =>
    render(
      <DetailsOverlayWrapper>
        <Search />
      </DetailsOverlayWrapper>,
    );

  it('should show live slider after typing search value', async () => {
    const { container, getByTestId, getByText } = setupSearch();
    const searchInput = getByTestId('SearchBox-Input');
    act(() => {
      fireEvent.change(searchInput, {
        target: { value: 'Och, Karol' },
      });
    });
    await waitFor(() => expect(getByText('Telewizja')));
    expect(getAllByText(container, /karol/i).length).toEqual(5);
  }, 60000);

  it('should open VOD details after clicked on search results', async () => {
    const { container, getByTestId, getByText } = setupSearch();
    const searchInput = getByTestId('SearchBox-Input');

    act(() => {
      fireEvent.change(searchInput, {
        target: { value: 'Star Trek' },
      });
    });
    await waitFor(() => expect(getByText('VOD')));
    const exampleVOD = getAllByTestId(
      container,
      'Search-VodPosterContainer',
    )[0];

    act(() => {
      fireEvent.click(exampleVOD);
    });
    await waitFor(() =>
      expect(getByText('Zobacz zwiastun')).toBeInTheDocument(),
    );
  });

  it('should show channels when search for tv', async () => {
    const { container, getByTestId, getByText } = setupSearch();
    const searchInput = getByTestId('SearchBox-Input');

    act(() => {
      fireEvent.change(searchInput, {
        target: { value: 'TVN' },
      });
    });
    await waitFor(() => expect(getByText('Telewizja')));

    waitFor(() => {
      expect(getByTestId('Channel-ChannelWrapper')).toBeInTheDocument();
      const exampleChannel = getByText('TVN HD');
      expect(exampleChannel).toBeInTheDocument();
    });
  });
});
