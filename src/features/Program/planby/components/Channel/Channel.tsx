import { ChannelWithPosition } from 'features/Program/planby/helpers/types';
import { getPublicAssetUrl } from 'utils/url';

import { ChannelProps } from './types';
import * as S from './styles';

export const Channel = <T extends ChannelWithPosition>({
  channel,
  onClick,
  ...rest
}: ChannelProps<T>) => {
  const { position, logo } = channel;
  return (
    <S.ChannelBox
      data-testid='sidebar-item'
      onClick={() => onClick?.(channel)}
      $top={position.top}
      $height={position.height}
      {...rest}
    >
      <S.ChannelLogo src={getPublicAssetUrl(logo)} alt='Logo' />
    </S.ChannelBox>
  );
};
