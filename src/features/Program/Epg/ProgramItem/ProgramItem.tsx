import { FC, PropsWithChildren, useMemo } from 'react';

import {
  <PERSON>Box,
  ProgramContent,
  ProgramFlex,
  ProgramImageWrapper,
  ProgramStack,
  ProgramTime,
  ProgramTitle,
  ProgramWrapper,
  useProgram,
} from 'features/Program/planby';
import { Image } from 'components/Image';
import { DetailsViewType, useDetailsView } from 'services/detailsView';
import { createProgramName } from 'utils/misc/createProgramName';
import { useHover } from 'hooks/useHover';

import { ProgramItemIcons } from './ProgramItemIcons';
import { ProgramProps } from './types';
import { ProgramItemButtons } from './ProgramItemButtons';

import { MINIMAL_CARD_WIDTH } from '../constants';

export const ProgramItem: FC<PropsWithChildren<ProgramProps>> = ({
  program,
}) => {
  const { setData: setDataDetailsView } = useDetailsView();
  const { styles, isLive, programTime } = useProgram({ program });
  const { hover, handleMouseEnter, handleMouseLeave } = useHover();

  const {
    data: {
      episodeNumber,
      image,
      id,
      title,
      isSubscribedChannel,
      isRecordingAllowed,
    },
  } = program;

  const canShowProgramImage = useMemo(
    () => image && isLive && styles.width > MINIMAL_CARD_WIDTH,
    [image, isLive, styles.width],
  );

  const programFullTitle = useMemo(
    () => (episodeNumber ? createProgramName(title, episodeNumber) : title),
    [episodeNumber, title],
  );

  return (
    <ProgramWrapper
      width={styles.width}
      style={styles.position}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      data-testid='ProgramItem-ProgramWrapper'
      title={programFullTitle}
    >
      <ProgramBox
        data-issubscribedprogram={isSubscribedChannel}
        data-isliveprogram={isLive}
        data-testid='ProgramItem-ProgramBox'
        width={styles.width}
        style={styles.position}
        onClick={() =>
          setDataDetailsView({
            type: DetailsViewType.Program,
            id: id,
            isSubscribedChannel: isSubscribedChannel,
            isChannelRecordingAllowed: isRecordingAllowed,
          })
        }
      >
        {hover && <ProgramItemButtons program={program} width={styles.width} />}
        <ProgramContent $width={styles.width} $isLive={isLive}>
          <ProgramFlex>
            {canShowProgramImage && (
              <ProgramImageWrapper>
                <Image src={image} alt={title} placeholderScale={0.5} />
              </ProgramImageWrapper>
            )}
            <ProgramStack>
              <ProgramTitle>{programFullTitle}</ProgramTitle>
              <ProgramTime width={styles.width}>{programTime}</ProgramTime>
            </ProgramStack>
          </ProgramFlex>
          <ProgramItemIcons program={program} width={styles.width} />
        </ProgramContent>
      </ProgramBox>
    </ProgramWrapper>
  );
};
