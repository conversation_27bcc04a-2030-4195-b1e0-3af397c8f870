import { MouseEvent, useCallback } from 'react';

import { useChannelRecording } from 'components/Channel/hooks';
import { ProgramItem } from 'features/Program/planby/helpers/types';
import { getLiveStatus } from 'utils/dateUtils';
import { useHandleProgramButtons } from 'hooks/useHandleProgramButtons';
import { useProgramButtonsVisibility } from 'hooks/useProgramButtonsVisibility';

export const useProgramItemButtons = (program: ProgramItem) => {
  const {
    data: {
      id,
      channelUuid: channelExtId,
      playFeatures,
      isStartoverDisabled,
      isRecordingAllowed,
      isSubscribedChannel,
      isRegionalTv,
      catchupDuration,
      isCatchupDisabled,
      till,
      startDate,
      endDate,
      since,
      prLevel,
    },
  } = program;

  const { isRecordingNow } = useChannelRecording(id);

  const {
    handlePlayCatchupProgram,
    handlePlayProgram,
    handlePlayStartoverProgram,
  } = useHandleProgramButtons(isSubscribedChannel, isRegionalTv);
  const {
    shouldShowCatchupButton,
    shouldShowPlayButton,
    shouldShowRecordingButton,
    shouldShowStartoverButton,
  } = useProgramButtonsVisibility({
    programId: id,
    isSubscribedChannel,
    startDate,
    endDate,
    playFeatures,
    catchupDuration,
    isCatchupDisabled,
    isStartoverDisabled,
    isRecordingAllowed,
  });
  const isLiveProgram = getLiveStatus(since, till);

  const handlePlayButton = useCallback(
    (event: MouseEvent) => {
      event.stopPropagation();
      handlePlayProgram(channelExtId, id, prLevel);
    },
    [channelExtId, handlePlayProgram, id, prLevel],
  );

  const handleCatchupButton = useCallback(
    (event: MouseEvent) => {
      event.stopPropagation();
      handlePlayCatchupProgram(channelExtId, id, endDate, prLevel);
    },
    [channelExtId, endDate, handlePlayCatchupProgram, id, prLevel],
  );

  const handleStartoverButton = useCallback(
    (event: MouseEvent) => {
      event.stopPropagation();
      handlePlayStartoverProgram(channelExtId, id, prLevel);
    },
    [channelExtId, handlePlayStartoverProgram, id, prLevel],
  );

  return {
    shouldShowPlayButton,
    shouldShowStartoverButton,
    shouldShowRecordingButton,
    isRecordingNow,
    shouldShowCatchupButton,
    isLiveProgram,
    handlePlayButton,
    handleCatchupButton,
    handleStartoverButton,
  };
};
