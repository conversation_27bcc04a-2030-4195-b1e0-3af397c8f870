import { FC, Fragment, PropsWithChildren, useRef } from 'react';

import { Loader } from 'components/Loader';
import { H2, Text } from 'components/Typography';
import { PosterPreview } from 'components/PosterPreview';
import { ParentalControlIcon } from 'components/ParentalControlIcon';
import { Slider } from 'components/Slider';
import { EpisodePreview } from 'components/EpisodePreview';
import { createProgramName } from 'utils/misc/createProgramName';
import { getPublicAssetUrl } from 'utils/url';

import { useDetailsProgram } from './hooks';
import { messages } from './messages';
import { DetailsProgramProps, EpisodeWithChannelDetails } from './types';
import * as S from './styles';
import { ButtonsControl } from './ButtonsControl';

export const DetailsProgram: FC<PropsWithChildren<DetailsProgramProps>> = ({
  dataDetailsView,
  setBackgroundImage,
  setData,
}) => {
  const detailsWrapperRef = useRef<HTMLDivElement>(null);
  const {
    formatMessage,
    isFetchingProgram,
    isFetchingSeries,
    isFetchingMedia,
    time,
    shortInfo,
    actors,
    playProgram,
    program,
    programChannel,
    playStartoverProgram,
    otherEpisodesOfProgram,
    handleOpenProgramDetails,
    indexOfEpisodeClosestToCurrentTime,
  } = useDetailsProgram({
    dataDetailsView,
    setBackgroundImage,
    setData,
    detailsWrapperRef,
  });

  const shortInfoRender = shortInfo?.map(({ name, info }) => {
    if (!info) return <Fragment key={name}></Fragment>;

    return (
      <p key={name}>
        <Text $sizeSmall $highlight>
          {name}:
        </Text>{' '}
        <Text $sizeSmall>{Array.isArray(info) ? info.join(', ') : info}</Text>
      </p>
    );
  });

  const uniqueActors = Array.from(
    new Map(
      actors?.map((actor) => [`${actor.name}-${actor.img}`, actor]),
    ).values(),
  );

  const actorsRender = uniqueActors.map(({ name, img }, index) => (
    <S.PosterContainer key={`${name}-${index}`}>
      <PosterPreview src={img} placeholderType='actor' title={name} />
    </S.PosterContainer>
  ));

  const otherEpisodesRender = otherEpisodesOfProgram?.map(
    (episode: EpisodeWithChannelDetails) => {
      const {
        name,
        programExtId,
        startTimeUtc,
        endTimeUtc,
        series,
        logoSignature,
        isSubscribedChannel,
        isChannelRecordingAllowed,
      } = episode;

      return (
        <S.EpisodeWrapper
          data-testid='DetailsProgram-EpisodeWrapper'
          key={programExtId}
        >
          <EpisodePreview
            name={name}
            logoSignature={logoSignature}
            onClickHandler={() => {
              handleOpenProgramDetails(
                programExtId,
                isSubscribedChannel,
                isChannelRecordingAllowed,
              );
            }}
            startDate={startTimeUtc}
            endDate={endTimeUtc}
            seasonName={series?.seasonNumber}
            episodeNumber={series?.episodeNumber}
            isCurrentViewedEpisode={programExtId === program?.programExtId}
          />
        </S.EpisodeWrapper>
      );
    },
  );

  const shouldRenderActors = Boolean(actorsRender?.length);
  const shouldRenderOtherEpisodes = Boolean(otherEpisodesRender?.length);

  if (isFetchingProgram || isFetchingSeries || isFetchingMedia || !program) {
    return <Loader />;
  }

  return (
    <S.DetailsWrapper
      ref={detailsWrapperRef}
      data-testid='DetailsProgram-Wrapper'
    >
      <S.TitleStyled>
        {createProgramName(program.name || '', program.series?.episodeNumber)}
        {program.prLevel && <ParentalControlIcon prLevel={+program.prLevel} />}
      </S.TitleStyled>
      <S.DarkText $sizeSmall>
        {program.series?.episodeTitle ? `${program.series.episodeTitle}, ` : ''}
        {time}
      </S.DarkText>
      <Text $sizeSmall>{program.genre}</Text>
      {programChannel ? (
        <ButtonsControl
          program={program}
          programChannel={programChannel}
          onPlay={playProgram}
          onPlayStartover={playStartoverProgram}
        />
      ) : (
        <S.Spacer />
      )}
      <S.Desc data-testid='DetailsProgram-Desc' $sizeMedium>
        {program.series?.episodeDescription || program.description}
      </S.Desc>
      {shortInfoRender}
      {shouldRenderOtherEpisodes && (
        <S.OtherEpisodesContainer>
          <H2>{formatMessage(messages.otherEpisodes)}</H2>
          <S.OtherEpisodesSliderWrapper data-testid='DetailsProgram-OtherEpisodesSliderWrapper'>
            <Slider
              indexScrollTo={indexOfEpisodeClosestToCurrentTime - 1}
              withButtons
            >
              {otherEpisodesRender}
            </Slider>
          </S.OtherEpisodesSliderWrapper>
        </S.OtherEpisodesContainer>
      )}
      {shouldRenderActors && (
        <S.ActorContainer>
          <H2>{formatMessage(messages.actors)}</H2>
          <S.ActorsSliderWrapper>
            <Slider withButtons>{actorsRender}</Slider>
          </S.ActorsSliderWrapper>
        </S.ActorContainer>
      )}
    </S.DetailsWrapper>
  );
};
