import { useCallback, useMemo } from 'react';

import { usePrepaidBalanceQuery } from 'services/api/oldApi/billing/queries';
import { useVodPurchasesAsset } from 'services/api/oldApi/vod/queries';
import { DetailsViewData } from 'services/detailsView/types';
import { VodPurchaseGift } from 'services/api/oldApi/vod/types';
import { useAuthenticationStatus } from 'services/user';

import { VodPrice } from './types';

import { PaymentMethod } from '../types';

export const useVodPurchaseDetails = (dataDetailsView: DetailsViewData) => {
  const { isAuthenticated } = useAuthenticationStatus();
  const { data: dataToVodPurchase, isFetching: isFetchingDataToVodPurchase } =
    useVodPurchasesAsset({ assetExternalId: dataDetailsView.id });
  const { data: prepaidBalanceData } = usePrepaidBalanceQuery(isAuthenticated);

  const isGiftAvailable = useMemo(
    () => dataToVodPurchase?.gifts && dataToVodPurchase.gifts?.length > 0,
    [dataToVodPurchase?.gifts],
  );

  const getOldestGift = useCallback(() => {
    if (isGiftAvailable) {
      const oldestGift = dataToVodPurchase!.gifts!.reduce(
        (previousGift: VodPurchaseGift, currentGift: VodPurchaseGift) =>
          currentGift.expirationDate < previousGift.expirationDate
            ? currentGift
            : previousGift,
        { expirationDate: Infinity, id: '' } as VodPurchaseGift,
      );
      return oldestGift;
    }
    return undefined;
  }, [dataToVodPurchase, isGiftAvailable]);

  const getPriceOfVodVersion = useCallback(
    (vodVersionExternalId: string): VodPrice => {
      const selectedVodVersion = dataToVodPurchase?.versions.find(
        (versionWithPrice) =>
          versionWithPrice.versionExternalId === vodVersionExternalId,
      );

      if (!selectedVodVersion) {
        return { withDiscount: false, price: '' };
      }

      if (isGiftAvailable && selectedVodVersion.discountPrice) {
        return {
          withDiscount: false,
          giftWithDiscount: true,
          price: selectedVodVersion.discountPrice,
        };
      }

      if (isGiftAvailable) {
        return { withDiscount: false, price: '0' };
      }

      if (selectedVodVersion.discountPrice) {
        return {
          withDiscount: true,
          price: selectedVodVersion.discountPrice,
          priceBeforeDiscount: selectedVodVersion.price,
        };
      }
      return { withDiscount: false, price: selectedVodVersion.price };
    },
    [dataToVodPurchase?.versions, isGiftAvailable],
  );

  const checkIfIsPrepaidAvailable = useCallback(
    (vodVersionExternalId: string) => {
      return Boolean(
        prepaidBalanceData &&
          Number(prepaidBalanceData?.balance) >=
            Number(getPriceOfVodVersion(vodVersionExternalId).price),
      );
    },
    [getPriceOfVodVersion, prepaidBalanceData],
  );

  const getVodPaymentMethod = useCallback(
    (vodVersionExternalId: string): PaymentMethod => {
      if (isGiftAvailable) {
        return PaymentMethod.gift;
      }
      if (checkIfIsPrepaidAvailable(vodVersionExternalId)) {
        return PaymentMethod.prepaid;
      }
      return PaymentMethod.postpaid;
    },
    [checkIfIsPrepaidAvailable, isGiftAvailable],
  );

  return {
    dataToVodPurchase,
    isFetchingDataToVodPurchase,
    isGiftAvailable,
    prepaidBalance: prepaidBalanceData?.balance,
    numberOfGifts: dataToVodPurchase?.gifts?.length,
    getOldestGiftId: getOldestGift,
    getPriceOfVodVersion,
    getVodPaymentMethod,
  };
};
