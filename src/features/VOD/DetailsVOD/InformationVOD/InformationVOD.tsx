import { FC, PropsWithChildren, useCallback, useMemo, useState } from 'react';
import { useIntl } from 'react-intl';

import { PrimaryButton } from 'components/Buttons/PrimaryButton';
import { ParentalControlIcon } from 'components/ParentalControlIcon';
import { VodPictograms } from 'components/VodPictograms/VodPictograms';
import { PosterPreview } from 'components/PosterPreview';
import { Slider } from 'components/Slider';
import { H2, Text } from 'components/Typography';
import { FavoriteButton } from 'components/Buttons/FavoriteButton';
import { ModalConfirmation } from 'components/ModalConfirmation';
import { useShortInfo } from 'hooks/useShortInfo/useShortInfo';
import { getFormattedPrice } from 'utils/priceUtils';
import { useAuthenticationStatus } from 'services/user';
import { useVodFavorite } from 'features/VOD/hooks';
import { useConfig } from 'services/config';
import { useModalPrControl } from 'services/user/ModalPrControl';
import { useCoreHouseholdQuery } from 'services/api/newApi/core/household';
import { getPublicAssetUrl } from 'utils/url';

import { InformationVODProps } from './types';
import { messages } from './messages';
import * as S from './styles';
import { useInformationVOD } from './hook';

import { useDetailsVOD, useVodPurchaseDetails } from '../hooks';

export const InformationVOD: FC<PropsWithChildren<InformationVODProps>> = ({
  setData,
  dataDetailsView,
  setBackgroundImage,
  setIsPurchaseProcessActive,
  setPurchaseInfo,
}) => {
  const { formatMessage } = useIntl();
  const { isFavorite, toggleFavorite } = useVodFavorite(dataDetailsView.id);
  const { getTechConfig } = useConfig();
  const { vods } = getTechConfig();
  const [isPurchaseDisabledModalVisible, setIsPurchaseDisabledModalVisible] =
    useState(false);

  const {
    idVODDetails,
    title,
    countries,
    year,
    genres,
    purchasedVODs,
    desc,
    directors,
    dubbing,
    actors,
    prLevel,
    isTrailerAvailable,
    pictograms,
    isVodAvailable,
    versions,
  } = useDetailsVOD({ dataDetailsView, setBackgroundImage });
  const { isAuthenticated } = useAuthenticationStatus();
  const { data: householdInfo } = useCoreHouseholdQuery(isAuthenticated);
  const { showPrControlModal } = useModalPrControl();
  const isVodPurchased = Boolean(purchasedVODs);

  const shortInfo = useShortInfo({
    directors,
    countries,
    year,
    dubbing,
  });

  const { dataToVodPurchase, getPriceOfVodVersion } =
    useVodPurchaseDetails(dataDetailsView);

  const { handleTrailerPlay, handleVodPlay, handleVodPurchase } =
    useInformationVOD({
      idVODDetails,
      purchasedVODs,
      isVodPurchased,
      dataToVodPurchase,
      prLevel,
      setData,
      setPurchaseInfo,
      setIsPurchaseProcessActive,
      setBackgroundImage,
      setIsPurchaseDisabledModalVisible,
    });

  const renderPlayButton = useCallback(() => {
    return (
      <PrimaryButton
        data-testid='DetailsVOD-HandleVODPlayButton'
        variant='orange'
        type='button'
        onClick={handleVodPlay}
      >
        <Text $primary>{formatMessage(messages.watch)}</Text>
      </PrimaryButton>
    );
  }, [formatMessage, handleVodPlay]);

  const renderPurchaseVodButtons = useCallback(() => {
    const shouldRenderButton = Boolean(versions?.tVodVersions);

    if (shouldRenderButton) {
      return versions?.tVodVersions?.map((version) => {
        const priceData = getPriceOfVodVersion(version.versionExternalId);
        return (
          <PrimaryButton
            key={version.versionExternalId}
            data-testid='DetailsVOD-HandleVODPurchaseButton'
            variant='orange'
            type='button'
            onClick={() => {
              if (householdInfo?.pinOnPurchase === 'required') {
                showPrControlModal(() => {
                  handleVodPurchase(version.versionExternalId);
                });
              } else {
                handleVodPurchase(version.versionExternalId);
              }
            }}
          >
            <Text $primary>
              {formatMessage(messages.vodQualities[version.definition], {
                price: getFormattedPrice(
                  priceData.giftWithDiscount ? '0' : priceData.price,
                ),
              })}
            </Text>
            {priceData.withDiscount && (
              <>
                &nbsp;
                <S.PriceBeforeDiscount>
                  {formatMessage(messages.priceWithoutDiscount, {
                    price: getFormattedPrice(priceData.priceBeforeDiscount!),
                  })}
                </S.PriceBeforeDiscount>
              </>
            )}
          </PrimaryButton>
        );
      });
    }
  }, [
    formatMessage,
    getPriceOfVodVersion,
    handleVodPurchase,
    householdInfo?.pinOnPurchase,
    showPrControlModal,
    versions?.tVodVersions,
  ]);

  const renderVodButtons = useCallback(() => {
    return (
      <>
        {isVodPurchased ? renderPlayButton() : renderPurchaseVodButtons()}
        {Boolean(isTrailerAvailable) && (
          <PrimaryButton
            data-testid='DetailsVOD-ShowTrailerButton'
            type='button'
            onClick={handleTrailerPlay}
          >
            <Text $primary>{formatMessage(messages.trailer)}</Text>
          </PrimaryButton>
        )}
        {vods.isFavouriteActionVisible && (
          <S.FavoriteStarContainer>
            <FavoriteButton
              handleClick={toggleFavorite}
              isFavorite={isFavorite}
              width={38}
              height={38}
            />
          </S.FavoriteStarContainer>
        )}
      </>
    );
  }, [
    formatMessage,
    handleTrailerPlay,
    isFavorite,
    isTrailerAvailable,
    isVodPurchased,
    renderPlayButton,
    renderPurchaseVodButtons,
    toggleFavorite,
    vods.isFavouriteActionVisible,
  ]);

  const shortInfoRender = useMemo(
    () =>
      shortInfo
        ?.filter(({ info }) => info)
        .map(({ name, info }) => {
          return (
            <p key={name}>
              <Text $highlight $sizeSmall>
                {name}:
              </Text>{' '}
              <Text $sizeSmall> {info}</Text>
            </p>
          );
        }),
    [shortInfo],
  );

  const actorsRender = actors?.map(({ name, img }) => (
    <S.PosterContainer key={name}>
      <PosterPreview
        src={getPublicAssetUrl(img || '')}
        title={name}
        placeholderType='actor'
      />
    </S.PosterContainer>
  ));

  const closePurchaseDisabledModal = useCallback(() => {
    setIsPurchaseDisabledModalVisible(false);
  }, []);

  const purchaseDisabledAsset = vods.assets.find(
    (asset) => asset.purchaseDisabled,
  )?.purchaseDisabled;

  const getFormattedCountryAndYear = (
    countriesParam?: string,
    yearParam?: string,
  ): string => {
    return [countriesParam, yearParam].filter(Boolean).join(', ');
  };

  const formattedCountryAndYear = getFormattedCountryAndYear(countries, year);

  return (
    <>
      {isPurchaseDisabledModalVisible && (
        <ModalConfirmation
          isOpen={isPurchaseDisabledModalVisible}
          modalTitle={formatMessage(messages.purchaseDisabledTitle)}
          modalDescription={purchaseDisabledAsset?.content}
          modalDescriptionType={purchaseDisabledAsset?.contentType}
          onClose={closePurchaseDisabledModal}
          buttonSubmitText='OK'
          onSubmit={closePurchaseDisabledModal}
        />
      )}
      <S.TitleContainer>
        <S.TitleStyled>{title}</S.TitleStyled>
        {Boolean(prLevel) && <ParentalControlIcon prLevel={prLevel!} />}
        {Boolean(pictograms) && <VodPictograms pictograms={pictograms} />}
      </S.TitleContainer>
      <S.DarkText $sizeSmall>{formattedCountryAndYear}</S.DarkText>
      <S.GenresContainer>
        <Text $sizeSmall>{genres}</Text>
      </S.GenresContainer>
      {isVodAvailable ? (
        <S.ButtonWrapper>{renderVodButtons()}</S.ButtonWrapper>
      ) : (
        <S.NotAvailableTextContainer>
          <Text $highlight>{formatMessage(messages.vodNotAvailable)}</Text>
          <S.FavoriteStarContainer>
            <FavoriteButton
              handleClick={toggleFavorite}
              isFavorite={isFavorite}
              width={38}
              height={38}
            />
          </S.FavoriteStarContainer>
        </S.NotAvailableTextContainer>
      )}
      <S.Desc $sizeMedium>{desc}</S.Desc>
      {shortInfoRender}
      {Boolean(actors) && Boolean(actorsRender) && (
        <S.ActorContainer>
          <H2>{formatMessage(messages.actors)}</H2>
          <Slider withButtons>{actorsRender!}</Slider>
        </S.ActorContainer>
      )}
    </>
  );
};
