import {
  createContext,
  FC,
  PropsWithChildren,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';

import { usePlayerInstance } from 'features/Player/BasePlayer/Context';
import { usePlayerType } from 'features/Player/Context';
import { useConfig } from 'services/config';
import { PlayerType } from 'features/Player/types';
import { globalConfig } from 'services/config/config';
import { useLocalStorage } from 'services/storage';
import {
  AudioOrSubsLanguage,
  useCoreHouseholdQuery,
  useSetLanguageMutation,
} from 'services/api/newApi/core/household';
import { useAuthenticationStatus } from 'services/user';

import {
  filterQualities,
  getAvailableSubtitles,
  getSelectedQuality,
  getSubtitleSettings,
  handleCodeLength,
} from './Options/helpers/getValues';
import {
  LANGUAGE_DEFAULT_STATE,
  QUALITY_DEFAULT_STATE,
} from './Options/constants';
import {
  Languages,
  PlayerSettingsContextValue,
  PlayerSettingsProviderProps,
  QualityType,
  SubtitlesLang,
  VolumeSettings,
} from './types';
import {
  ACCEPTABLE_AUDIO_LANGUAGES,
  ACCEPTABLE_SUBTITLES_LANGUAGES,
  DEFAULT_AUDIO,
  DEFAULT_SUBTITLES,
  DEFAULT_VOLUME_SETTINGS,
} from './constants';

const PlayerSettingsContext = createContext<PlayerSettingsContextValue>(
  {} as PlayerSettingsContextValue,
);

export const PlayerSettingsProvider: FC<
  PropsWithChildren<PlayerSettingsProviderProps>
> = ({ children }) => {
  const { player } = usePlayerInstance();
  const { config } = useConfig();
  const {
    profile: { defaultAudio, defaultSubs },
  } = globalConfig;
  const playerType = usePlayerType();
  const { isAuthenticated } = useAuthenticationStatus();
  const { data: householdInfo } = useCoreHouseholdQuery(isAuthenticated);

  const { mutate: changeLanguage } = useSetLanguageMutation();

  const [videoQualities, setVideoQualities] = useState<QualityType>(
    QUALITY_DEFAULT_STATE,
  );
  const [languages, setLanguages] = useState<Languages>(LANGUAGE_DEFAULT_STATE);
  const [subtitlesLanguage, setSubtitlesLanguage] = useState<SubtitlesLang>(
    LANGUAGE_DEFAULT_STATE,
  );
  const [isAutomaticQuality, setIsAutomaticQuality] = useState(true);

  const [activeAudioLanguage, setActiveAudioLanguage] =
    useState<AudioOrSubsLanguage>(defaultAudio);
  const [activeSubtitles, setActiveSubtitles] =
    useState<AudioOrSubsLanguage>(defaultSubs);
  const [volumeSettings, setVolumeSettings] = useLocalStorage<VolumeSettings>(
    'volumeSettings',
    DEFAULT_VOLUME_SETTINGS,
  );

  const {
    appConfig: {
      player: { maxVerticalResolution, maxVerticalResolutionVod },
    },
  } = config;

  const maxResolution =
    playerType === PlayerType.Vod || playerType === PlayerType.Trailer
      ? maxVerticalResolutionVod
      : maxVerticalResolution;

  const filteredQualities = useMemo(
    () => filterQualities(player?.qualities, maxResolution),
    [maxResolution, player?.qualities],
  );
  const handleAudioAndSubtitlesValues = useCallback(() => {
    if (player && player.audioTracks) {
      const activePlayerAudioTrack = player.audioTracks[player.audioTrack]
        .language as AudioOrSubsLanguage;

      setActiveAudioLanguage(activePlayerAudioTrack);

      setLanguages({
        available: player?.audioTracks,
        selected: player?.audioTracks[player.audioTrack],
      });
    }
    if (player && player.textTracks) {
      const activePlayerTextTrack = getSubtitleSettings(player)
        .language as AudioOrSubsLanguage;

      setActiveSubtitles(activePlayerTextTrack);

      setSubtitlesLanguage({
        available: getAvailableSubtitles(player),
        selected: getSubtitleSettings(player),
      });
    }
  }, [player]);

  const handleQualitiesValues = useCallback(() => {
    if (player && filteredQualities) {
      setVideoQualities({
        available: filteredQualities,
        selectedQuality: getSelectedQuality(
          filteredQualities,
          isAutomaticQuality,
        ),
      });
    }
  }, [filteredQualities, isAutomaticQuality, player]);

  const handleUserChangeAudio = useCallback(() => {
    if (player && player.audioTracks) {
      const activePlayerAudioTrack = handleCodeLength(
        player.audioTracks[player.audioTrack].language as AudioOrSubsLanguage,
      );
      const isAcceptableAudioTrack = ACCEPTABLE_AUDIO_LANGUAGES.includes(
        activePlayerAudioTrack,
      );

      changeLanguage({
        audioLang: isAcceptableAudioTrack
          ? activePlayerAudioTrack
          : DEFAULT_AUDIO,
        subsLang: householdInfo?.subsLang || DEFAULT_SUBTITLES,
      });
    }
  }, [changeLanguage, householdInfo?.subsLang, player]);

  const handleUserChangeSubtitles = useCallback(() => {
    const activePlayerTextTrack = handleCodeLength(
      getSubtitleSettings(player).language as AudioOrSubsLanguage,
    );
    const isAcceptableSubtitlesTrack = ACCEPTABLE_SUBTITLES_LANGUAGES.includes(
      activePlayerTextTrack,
    );

    changeLanguage({
      audioLang: householdInfo?.audioLang || DEFAULT_AUDIO,
      subsLang: isAcceptableSubtitlesTrack
        ? activePlayerTextTrack
        : DEFAULT_SUBTITLES,
    });
  }, [changeLanguage, householdInfo?.audioLang, player]);

  useEffect(() => {
    if (player) {
      player.on('trackschanged', handleAudioAndSubtitlesValues);

      player.on('qualitychanged', handleQualitiesValues);
    }

    return () => {
      player?.off('trackschanged', handleAudioAndSubtitlesValues);
      player?.off('qualitychanged', handleQualitiesValues);
    };
  }, [
    player,
    filteredQualities,
    handleAudioAndSubtitlesValues,
    handleQualitiesValues,
  ]);

  const contextValue = useMemo(
    () => ({
      videoQualities,
      setVideoQualities,
      filteredQualities,
      languages,
      subtitlesLanguage,
      isAutomaticQuality,
      setIsAutomaticQuality,
      activeAudioLanguage,
      activeSubtitles,
      handleUserChangeAudio,
      handleUserChangeSubtitles,
      volumeSettings,
      setVolumeSettings,
    }),
    [
      videoQualities,
      setVideoQualities,
      filteredQualities,
      languages,
      subtitlesLanguage,
      isAutomaticQuality,
      setIsAutomaticQuality,
      activeAudioLanguage,
      activeSubtitles,
      handleUserChangeAudio,
      handleUserChangeSubtitles,
      volumeSettings,
      setVolumeSettings,
    ],
  );

  return (
    <PlayerSettingsContext.Provider value={contextValue}>
      {children}
    </PlayerSettingsContext.Provider>
  );
};

export const usePlayerSettingsValues = (): PlayerSettingsContextValue => {
  const context = useContext(PlayerSettingsContext);

  if (context) {
    return context;
  }

  throw new Error('Component beyond PlayerSettingsContext');
};
