import { useMemo } from 'react';
import { useIntl } from 'react-intl';

import { IconPlayerStartover } from 'components/Icons/player';
import { usePlayerActions } from 'features/Player/BasePlayer/Hook';
import {
  isCatchupPlayback,
  isChannelPlayback,
  isRecordingPlayback,
  isStartoverPlayback,
  isTrailerPlayback,
  isVodPlayback,
  usePlayerFeatures,
  usePlayerPlayback,
  usePlayerProgram,
} from 'features/Player/Context';
import { usePlayContent, useProgramTimer } from 'features/Player/Hook';
import { useModalNpvr } from 'services/user/ModalNpvr';
import { useUserProfile } from 'services/user';

import { messages } from './messages';
import * as S from './styles';

export const StartoverButton = () => {
  const { formatMessage } = useIntl();
  const { playback } = usePlayerPlayback();
  const { program } = usePlayerProgram();
  const { jumpTo } = usePlayerActions();
  const { playStartover } = usePlayContent();
  const { showNpvrModal } = useModalNpvr();
  const { isNpvrService } = useUserProfile();

  const { startPosition } = useProgramTimer({
    startTimeUtc: program?.startTimeUtc,
    endTimeUtc: program?.endTimeUtc,
  });

  const {
    features: { startOver },
  } = usePlayerFeatures();

  const isNotRestrictedContent = useMemo(
    () =>
      isVodPlayback(playback) ||
      isTrailerPlayback(playback) ||
      isRecordingPlayback(playback),
    [playback],
  );

  const isEnabled = useMemo(() => {
    if (isNotRestrictedContent) return true;
    return startOver;
  }, [isNotRestrictedContent, startOver]);

  const onClick = () => {
    if (
      !isNpvrService &&
      !isVodPlayback(playback) &&
      !isRecordingPlayback(playback) &&
      !isTrailerPlayback(playback)
    ) {
      return showNpvrModal();
    }

    if (isStartoverPlayback(playback) || isCatchupPlayback(playback)) {
      jumpTo(startPosition);
    }

    if (isChannelPlayback(playback)) {
      const { channelExtId, programExtId } = program;

      playStartover({ ...playback, channelExtId, programExtId });
    }

    if (isNotRestrictedContent) {
      jumpTo(0);
    }
  };

  return (
    <S.ExpandedButtonWrapper onClick={onClick} disabled={!isEnabled}>
      <IconPlayerStartover />
      <S.TextStyled>{formatMessage(messages.startover)}</S.TextStyled>
    </S.ExpandedButtonWrapper>
  );
};
