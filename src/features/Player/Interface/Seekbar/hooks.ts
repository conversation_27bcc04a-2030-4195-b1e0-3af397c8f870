import { useMemo, useState } from 'react';
import { Player } from 'voplayer-html5';
import { DebouncedState, useDebouncedCallback } from 'use-debounce';

import {
  PlayerFeatures,
  usePlayerFeatures,
  usePlayerProgram,
} from 'features/Player/Context';
import { isEmpty } from 'utils/misc/isEmpty';
import { useAuthenticationStatus, useUserProfile } from 'services/user';

import { ScrubbingMode, TimePreview } from './types';
import { DELAY_TO_DOWNLOAD_THUMBNAIL_AFTER_MOUSE_MOVE_STOP } from './constants';

export const useLiveTimeEdges = (features: PlayerFeatures) => {
  const { startOver, catchup, leadTime, lagTime } = features;
  const { program } = usePlayerProgram();
  const { startTimeUtc, endTimeUtc } = program;

  if (isEmpty(program)) {
    return {
      startDate: 0,
      endDate: 0,
    };
  }
  if (startOver || catchup) {
    return {
      startDate: startTimeUtc - leadTime,
      endDate: endTimeUtc + lagTime,
    };
  }
  return {
    startDate: startTimeUtc,
    endDate: endTimeUtc,
  };
};
export const useSeekbarTimeLabel = (scrubbingEnabled?: boolean) => {
  const [timePreview, setTimePreview] = useState(TimePreview.NONE);

  const showTimeLabel = (type: TimePreview) => {
    if (
      import.meta.env.VITE_REACT_APP_IS_SCRUBBING_WITH_THUMBNAILS_DISABLED ===
      'true'
    ) {
      setTimePreview(TimePreview.TIME_ONLY);
      return;
    }
    if (!scrubbingEnabled) {
      setTimePreview(TimePreview.TIME_ONLY);
      return;
    }
    setTimePreview(type);
  };

  const hideTimeLabel = () => {
    setTimePreview(TimePreview.NONE);
  };

  return { timePreview, showTimeLabel, hideTimeLabel };
};

export const useThumbnails = (player: Player | null) => {
  const [currentThumbnailUrl, setCurrentThumbnailUrl] = useState('');

  const setNewThumbnailUrl: DebouncedState<
    (thumbnailTimestamp: number) => void
  > = useDebouncedCallback((thumbnailTimestamp: number) => {
    if (player?.hasThumbnails()) {
      player?.getThumbnail(thumbnailTimestamp).then((thumbnail) => {
        setCurrentThumbnailUrl(thumbnail?.url || '');
      });
    }
  }, DELAY_TO_DOWNLOAD_THUMBNAIL_AFTER_MOUSE_MOVE_STOP);
  return {
    currentThumbnailUrl,
    setNewThumbnailUrl,
  };
};

export const useScrubbingMode = () => {
  const { isDynamicCatchup } = usePlayerProgram();
  const { isAuthenticated } = useAuthenticationStatus();
  const { isNpvrService } = useUserProfile();
  const { features } = usePlayerFeatures();

  const scrubbingMode = useMemo((): ScrubbingMode => {
    if (!features.startOver || !isNpvrService || !isAuthenticated) {
      return 'fullBlock';
    }

    if (isDynamicCatchup) {
      return 'full';
    }

    if (!features.fastForward) {
      return 'blockForward';
    }

    return 'full';
  }, [
    features.fastForward,
    features.startOver,
    isAuthenticated,
    isDynamicCatchup,
    isNpvrService,
  ]);

  const scrubbingEnabled = useMemo(
    () => scrubbingMode !== 'fullBlock',
    [scrubbingMode],
  );

  return { scrubbingMode, scrubbingEnabled };
};
