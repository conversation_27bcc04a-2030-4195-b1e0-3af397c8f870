import {
  Info<PERSON>utton,
  MiniToExpandedSwitchButton,
  PlayPauseButton,
  SettingsButton,
  StartoverButton,
  ToggleFullScreenButton,
  VolumeButton,
} from 'features/Player/Interface/Button';
import { RecordingSeekbar } from 'features/Player/Interface/Seekbar';
import { usePlayerRecording } from 'features/Player/RecordingPlayer/Context';
import { useTheme } from 'theme';
import { useConfig } from 'services/config';
import { createProgramName } from 'utils/misc/createProgramName';

import * as S from './styles';

import { ChannelLabel } from '../ChannelLabel';

export const RecordingFullScreenPlayerControls = () => {
  const theme = useTheme();
  const { recording } = usePlayerRecording();
  const { getTechConfig } = useConfig();
  const { withVisibleNpvrFeatures } = getTechConfig();

  return (
    <S.PlayerControlsContainer>
      <S.TopWrapper>
        <ChannelLabel
          name={createProgramName(
            recording.name || '',
            recording.episodeNumber,
          )}
        />
        <RecordingSeekbar scrubbingMode={'full'} />
      </S.TopWrapper>
      <S.PlayerControls>
        <S.InfoButtonWrapper>
          <InfoButton
            fileId={recording.recordingExtId}
            iconColorHover={theme.colors.info}
          />
        </S.InfoButtonWrapper>
        <S.CenterButtons $numberOfChildren={2}>
          {withVisibleNpvrFeatures && <StartoverButton />}
          <PlayPauseButton />
        </S.CenterButtons>
        <S.AsideButtons>
          <VolumeButton />
          <SettingsButton />
          <MiniToExpandedSwitchButton />
          <ToggleFullScreenButton />
        </S.AsideButtons>
      </S.PlayerControls>
    </S.PlayerControlsContainer>
  );
};
