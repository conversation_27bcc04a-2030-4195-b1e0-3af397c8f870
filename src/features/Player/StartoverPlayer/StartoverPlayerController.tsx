import { FC } from 'react';

import {
  useLiveContinueWatchingHandler,
  useProgramEndEvent,
  useRefreshLiveSession,
} from 'features/Player/Hook';

import { StartoverPlayerControllerProps } from './types';

import { PlayerType } from '../types';
import { useContinueEndedContent } from '../Hook/useContinueEndedProgram';

export const StartoverPlayerController: FC<
  React.PropsWithChildren<StartoverPlayerControllerProps>
> = ({ playback, children }) => {
  const { channelExtId } = playback;

  useProgramEndEvent({ timerType: 'timestamp' });
  useContinueEndedContent(PlayerType.Startover);
  useRefreshLiveSession({ channelExtId });
  useLiveContinueWatchingHandler();

  return <>{children}</>;
};
