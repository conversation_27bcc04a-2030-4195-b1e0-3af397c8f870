import { FC, PropsWithChildren } from 'react';

import { BasePlayer } from 'features/Player/BasePlayer';
import {
  PlayerFeaturesProvider,
  PlayerProgramProvider,
  usePlayerProgram,
} from 'features/Player/Context';
import { useHandlePlayInfoError, useProgramTimer } from 'features/Player/Hook';
import { useChannelTimeShiftingPlayInfo } from 'services/api/newApi/live/channels';

import { StartoverPlayerController } from './StartoverPlayerController';
import {
  StartoverPlayerInnerWrapperProps,
  StartoverPlayerProps,
  StartoverPlayerWrapperProps,
} from './types';

export const StartoverPlayerInnerWrapper: FC<
  PropsWithChildren<StartoverPlayerInnerWrapperProps>
> = (props) => {
  const { playback, playInfo, program, children } = props;
  const { startPosition: playbackStartPosition, muted, loop } = playback;
  const { startTimeUtc, endTimeUtc } = program;

  const { startPosition, getResumePosition } = useProgramTimer({
    startTimeUtc,
    endTimeUtc,
  });

  const resumePosition = playbackStartPosition
    ? getResumePosition(playbackStartPosition)
    : startPosition;

  return (
    <BasePlayer
      mediaUrl={playInfo.streamUrl}
      casToken={playInfo.casToken}
      startPosition={resumePosition}
      loop={loop}
    >
      <StartoverPlayerController playback={playback}>
        {children}
      </StartoverPlayerController>
    </BasePlayer>
  );
};

export const StartoverPlayerWrapper: FC<
  PropsWithChildren<StartoverPlayerWrapperProps>
> = (props) => {
  const { playback, children } = props;
  const { channelExtId, programExtId } = playback;

  const { handlePlayInfoError } = useHandlePlayInfoError();
  const { program, channel, isDynamicCatchup } = usePlayerProgram();
  const { data: playInfo, refetch: refetchPlayInfo } =
    useChannelTimeShiftingPlayInfo(
      {
        channelExtId,
        programExtId,
        timeShiftingService: isDynamicCatchup ? 'catchup' : 'stov',
      },
      true,
      (error: unknown) => handlePlayInfoError(error, refetchPlayInfo),
    );

  if (!playInfo) {
    return <></>;
  }

  return (
    <PlayerFeaturesProvider
      channel={channel}
      program={program}
      withConfigLagTime
      withConfigLeadTime
    >
      <StartoverPlayerInnerWrapper
        playback={playback}
        playInfo={playInfo}
        program={program}
        refetchToken={refetchPlayInfo}
      >
        {children}
      </StartoverPlayerInnerWrapper>
    </PlayerFeaturesProvider>
  );
};

export const StartoverPlayer: FC<PropsWithChildren<StartoverPlayerProps>> = (
  props,
) => {
  const { playback, children } = props;
  const { channelExtId, programExtId } = playback;

  return (
    <PlayerProgramProvider channelId={channelExtId} programId={programExtId}>
      <StartoverPlayerWrapper playback={playback}>
        {children}
      </StartoverPlayerWrapper>
    </PlayerProgramProvider>
  );
};
