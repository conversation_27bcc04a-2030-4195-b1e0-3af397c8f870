import { useIntl } from 'react-intl';
import { useCallback } from 'react';

import { OvalModal } from 'components/OvalModal';
import { CatchupPlayback, usePlayerPlayback } from 'features/Player/Context';
import { PlayerMode, PlayerType } from 'features/Player/types';
import { PLAYER_TIME_LIMIT } from 'services/api/oldApi';
import { useAppLayoutMode } from 'services/appLayoutMode';
import { useModalPrControl } from 'services/user/ModalPrControl';
import {
  PlayerOverlayContent,
  usePlayerOverlay,
} from 'features/Player/PlayerOverlay';
import { usePlayerActions } from 'features/Player/BasePlayer/Hook';

import { modalMessages } from './messages';
import * as S from './styles';

const { BACK_TO_LIVE_ANIMATION_TIME } = PLAYER_TIME_LIMIT;

export const BackToLiveModal = ({
  withParentalControl,
}: {
  withParentalControl?: boolean;
}) => {
  const { formatMessage } = useIntl();
  const { setPlayback, reset } = usePlayerPlayback();
  const { showPrControlModal } = useModalPrControl();
  const { jumpToLive } = usePlayerActions();
  const { mode: playerMode } = useAppLayoutMode();
  const { setPlayerOverlay } = usePlayerOverlay();

  const backToLiveProgram = useCallback(() => {
    const playLive = () => {
      setPlayback((currentPlayback) => {
        const { type, programExtId, ...rest } =
          currentPlayback as CatchupPlayback;
        return { type: PlayerType.Channel, ...rest };
      });
      jumpToLive(); // we have to jump to live time here in case when we are still on channel player and playback do not change (time shifted live)
    };

    if (withParentalControl) {
      showPrControlModal(() => {
        playLive();
      }, reset);
      return;
    }

    playLive();
  }, [jumpToLive, reset, setPlayback, showPrControlModal, withParentalControl]);

  const handleBackToLiveProgram = useCallback(() => {
    backToLiveProgram();
    setPlayerOverlay(PlayerOverlayContent.Default);
  }, [backToLiveProgram, setPlayerOverlay]);

  if (playerMode === PlayerMode.Mini || playerMode === PlayerMode.Background) {
    handleBackToLiveProgram();
    return null;
  }

  return (
    <S.ModalPosition>
      <OvalModal
        isOpen={true}
        onClose={handleBackToLiveProgram}
        onClick={handleBackToLiveProgram}
        modalTitle={formatMessage(modalMessages.backToLiveModalTitle)}
        modalDesc={formatMessage(modalMessages.backToLiveModalDescription)}
        timeout={BACK_TO_LIVE_ANIMATION_TIME}
        isStick={false}
      />
    </S.ModalPosition>
  );
};
