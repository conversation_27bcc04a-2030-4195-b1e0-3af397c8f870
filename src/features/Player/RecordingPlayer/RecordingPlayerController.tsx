import React from 'react';

import { useRecordingContinueWatchingHandler } from './Hook';
import { RecordingPlayerControllerProps } from './types';

import { useRefreshLiveSession } from '../Hook';

export const RecordingPlayerController: React.FC<
  React.PropsWithChildren<RecordingPlayerControllerProps>
> = ({ playback, children }) => {
  const { channelExtId } = playback;

  useRefreshLiveSession({ channelExtId });
  useRecordingContinueWatchingHandler();

  return <>{children}</>;
};
