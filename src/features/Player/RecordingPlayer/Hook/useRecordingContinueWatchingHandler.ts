import { useCallback } from 'react';

import { usePlayerTimer } from 'features/Player/BasePlayer/Context';
import {
  useGenericContinueWatchingHandler,
  useProgramTimer,
} from 'features/Player/Hook';
import { usePlayerRecording } from 'features/Player/RecordingPlayer/Context';
import { PlayerState } from 'features/Player/types';
import { useRecentlyWatchedRecordingMutation } from 'services/api/oldApi/myzone';
import { useLogger } from 'services/logger';

export const useRecordingContinueWatchingHandler = () => {
  const { logger } = useLogger();
  const { recording } = usePlayerRecording();
  const {
    timer: { currentTime },
  } = usePlayerTimer();
  const { startDateEpg, endDateEpg } = recording;

  const { getContinueWatchingPosition } = useProgramTimer({
    startTimeUtc: startDateEpg || 0,
    endTimeUtc: endDateEpg || 0,
  });

  const { mutate } = useRecentlyWatchedRecordingMutation();

  const handler = useCallback(() => {
    const { programExtId, channelExtId, recordingExtId } = recording;

    const resumePosition = getContinueWatchingPosition(currentTime);

    logger.debug('Adding to continue watching:', {
      programExtId,
      channelExtId,
      recordingExtId,
      resumePosition,
    });

    mutate({
      programExternalId: programExtId,
      channelExternalId: channelExtId,
      otfRecordingId: recordingExtId,
      resumePosition,
    });
  }, [recording, getContinueWatchingPosition, currentTime, logger, mutate]);

  useGenericContinueWatchingHandler({
    handler,
    onPlaybackStates: [PlayerState.Pause],
    onUnmount: true,
  });
};
