import { RecordingPlayback } from 'features/Player/Context';
import { ChannelPlayInfoResponse } from 'services/api/newApi/live/channels';
import { RecordingDetails } from 'services/api/newApi/optional/npvr';

export interface RecordingPlayerProps {
  playback: RecordingPlayback;
}

export interface RecordingPlayerWrapperProps {
  playback: RecordingPlayback;
}

export interface RecordingPlayerInnerWrapperProps {
  playback: RecordingPlayback;
  playInfo: ChannelPlayInfoResponse;
  recording: RecordingDetails;
}

export interface RecordingPlayerControllerProps {
  playback: RecordingPlayback;
}
