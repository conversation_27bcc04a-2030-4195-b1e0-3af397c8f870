import { FC, PropsWithChildren } from 'react';

import { PlayerFeaturesProvider } from 'features/Player/Context';
import { BasePlayer } from 'features/Player/BasePlayer';
import { useHandlePlayInfoError, useProgramTimer } from 'features/Player/Hook';
import { useUserProfile } from 'services/user';
import { useChannelTimeShiftingPlayInfo } from 'services/api/newApi/live/channels';

import { PlayerRecordingProvider, usePlayerRecording } from './Context';
import { RecordingPlayerController } from './RecordingPlayerController';
import {
  RecordingPlayerInnerWrapperProps,
  RecordingPlayerProps,
  RecordingPlayerWrapperProps,
} from './types';

export const RecordingPlayerInnerWrapper: FC<
  PropsWithChildren<RecordingPlayerInnerWrapperProps>
> = (props) => {
  const { playback, playInfo, recording, children } = props;
  const { startPosition: playbackStartPosition, loop } = playback;
  const { startDateEpg, endDateEpg } = recording;

  const { startPosition, getResumePosition } = useProgramTimer({
    startTimeUtc: startDateEpg || 0,
    endTimeUtc: endDateEpg || 0,
  });

  const resumePosition = playbackStartPosition
    ? getResumePosition(playbackStartPosition)
    : startPosition;

  return (
    <BasePlayer
      mediaUrl={playInfo.streamUrl}
      casToken={playInfo.casToken}
      startPosition={resumePosition}
      loop={loop}
    >
      <RecordingPlayerController playback={playback}>
        {children}
      </RecordingPlayerController>
    </BasePlayer>
  );
};

export const RecordingPlayerWrapper: FC<
  PropsWithChildren<RecordingPlayerWrapperProps>
> = (props) => {
  const { playback, children } = props;

  const { handlePlayInfoError } = useHandlePlayInfoError();
  const { recording } = usePlayerRecording();
  const { userHhTech } = useUserProfile();
  const { data: playInfo, refetch: refetchPlayInfo } =
    useChannelTimeShiftingPlayInfo(
      {
        channelExtId: recording.channelExtId,
        programExtId: recording.programExtId,
        recordingId: recording.recordingExtId,
        timeShiftingService: 'npvr',
      },
      true,
      (error: unknown) => handlePlayInfoError(error, refetchPlayInfo),
    );

  if (!playInfo) {
    return <></>;
  }

  return (
    <PlayerFeaturesProvider
      recording={recording}
      withConfigLagTime
      withConfigLeadTime
    >
      <RecordingPlayerInnerWrapper
        playback={playback}
        playInfo={playInfo}
        recording={recording}
      >
        {children}
      </RecordingPlayerInnerWrapper>
    </PlayerFeaturesProvider>
  );
};

export const RecordingPlayer: FC<PropsWithChildren<RecordingPlayerProps>> = (
  props,
) => {
  const { playback, children } = props;
  const { recordingExtId } = playback;

  return (
    <PlayerRecordingProvider recordingExtId={recordingExtId}>
      <RecordingPlayerWrapper playback={playback}>
        {children}
      </RecordingPlayerWrapper>
    </PlayerRecordingProvider>
  );
};
