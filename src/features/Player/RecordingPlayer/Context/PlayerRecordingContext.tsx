import { createContext, FC, PropsWithChildren, useContext } from 'react';

import {
  RecordingDetails,
  useRecordingDetailsQuery,
} from 'services/api/newApi/optional/npvr';

import {
  PlayableRecording,
  PlayerRecordingContextValue,
  PlayerRecordingProviderProps,
} from './types';

const isPlayableRecording = (rec: RecordingDetails): rec is PlayableRecording =>
  Boolean(rec.channelExtId && rec.programExtId && rec.recordingExtId);

const PlayerRecordingContext = createContext<PlayerRecordingContextValue>(
  {} as PlayerRecordingContextValue,
);

export const PlayerRecordingProvider: FC<
  PropsWithChildren<PlayerRecordingProviderProps>
> = ({ recordingExtId, children }) => {
  const { data: recording } = useRecordingDetailsQuery({ recordingExtId });

  if (!recording || !isPlayableRecording(recording)) {
    return <></>;
  }

  return (
    <PlayerRecordingContext.Provider value={{ recording }}>
      {children}
    </PlayerRecordingContext.Provider>
  );
};

export const usePlayerRecording = (): PlayerRecordingContextValue => {
  const context = useContext(PlayerRecordingContext);

  if (context) {
    return context;
  }
  throw new Error('Component beyond PlayerRecordingContext');
};
