import { RecordingId } from 'services/api/common/types';
export interface PlayerRecordingProviderProps {
  recordingExtId: RecordingId;
}

export interface PlayerRecordingContextValue {
  recording: PlayableRecording;
}

export interface PlayableRecording {
  channelExtId: string;
  programExtId: string;
  recordingExtId: string;
  name?: string;
  episodeNumber?: string;
  startDateEpg?: number;
  endDateEpg?: number;
}
