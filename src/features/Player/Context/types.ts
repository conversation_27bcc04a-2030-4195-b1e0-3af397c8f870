import React from 'react';
import { AudioTrack, TextTrack } from 'voplayer-html5/lib/src/player/Types';

import { PlayerType } from 'features/Player/types';
import {
  AssetId,
  ChannelId,
  ProgramId,
  RecordingId,
  TimeInSeconds,
  VersionId,
} from 'services/api/common/types';
import { Channel } from 'services/api/newApi/live/channels';
import {
  ProgramDetails,
  ScheduleProgram,
} from 'services/api/newApi/core/tvguide';
import { VodAsset } from 'services/api/oldApi/vod';
import { AudioOrSubsLanguage } from 'services/api/newApi/core/household';
import { RecordingDetails } from 'services/api/newApi/optional/npvr';

export type InitialPlayback = {
  isInitial: true;
};

type BasePlayback = {
  muted?: boolean;
  loop?: boolean;
  startPosition?: TimeInSeconds;
};

export type TrailerPlayback = BasePlayback & {
  type: PlayerType.Trailer;
  assetExternalId: AssetId;
};

export type VodPlayback = BasePlayback & {
  type: PlayerType.Vod;
  assetExternalId: AssetId;
  versionExternalId: VersionId;
};

export type RecordingPlayback = BasePlayback & {
  type: PlayerType.Recording;
  channelExtId: ChannelId;
  recordingExtId: RecordingId;
};

export type CatchupPlayback = BasePlayback & {
  type: PlayerType.Catchup;
  channelExtId: ChannelId;
  programExtId: ProgramId;
};

export type StartoverPlayback = BasePlayback & {
  type: PlayerType.Startover;
  channelExtId: ChannelId;
  programExtId: ProgramId;
};

export type ChannelPlayback = Omit<BasePlayback, 'startPosition'> & {
  type: PlayerType.Channel;
  channelExtId: ChannelId;
  isFree?: boolean;
};

export type PlayerPlayback =
  | InitialPlayback
  | TrailerPlayback
  | VodPlayback
  | RecordingPlayback
  | CatchupPlayback
  | StartoverPlayback
  | ChannelPlayback;

export interface PlayerPlaybackContextValue {
  playback: PlayerPlayback;
  setPlayback: React.Dispatch<React.SetStateAction<PlayerPlayback>>;
  reset: () => void;
}

export interface PlayerProgramProviderProps {
  channelId: ChannelId;
  programId?: ProgramId;
}

export interface PlayerProgramContextValue {
  channel: Channel;
  program: ProgramDetails;
  isDynamicCatchup: boolean;
  loadProgram: (programId: ProgramId) => void;
  loadCurrentProgram: () => void;
  getNextProgram: () => ScheduleProgram | null;
  getCurrentLiveProgram: () => ScheduleProgram | null;
}

export interface PlayerVodProviderProps {
  assetExternalId: AssetId;
}

export interface PlayerVodContextValue {
  vod: VodAsset;
}

export type PlayerFeaturesProviderProps = {
  withConfigLeadTime?: boolean;
  withConfigLagTime?: boolean;
} & (
  | {
      channel?: never;
      program?: never;
      recording?: never;
    }
  | {
      channel: Channel;
      program: ProgramDetails;
      recording?: never;
    }
  | {
      channel?: never;
      program?: never;
      recording: RecordingDetails;
    }
);

export interface PlayerFeatures {
  blackout: boolean;
  fastForward: boolean;
  catchup: boolean;
  npvr: boolean;
  startOver: boolean;
  leadTime: number;
  lagTime: number;
}

export interface PlayerFeaturesContextValue {
  features: PlayerFeatures;
}

export type ErrorType =
  | 'PLAYER_BLACKOUT'
  | 'CDN_ERROR'
  | 'EXPIRED_ERROR'
  | 'HOME_NETWORK_ERROR';

export type ErrorMessage = {
  message: string;
  button?: {
    id: string;
    defaultMessage: string;
  };
};

export type ErrorMap = Record<ErrorType, ErrorMessage>;

export type PlayerErrorScreenContextValue = {
  showErrorScreen(errorType: ErrorType, screenHandler?: () => void): void;
  closeErrorScreen(): void;
  errorMessage: ErrorMessage;
  buttonHandler: () => void | undefined;
};

export type PlayerErrorScreenProviderProps = {};

export type PlayerLanguagesProviderProps = {};

export enum LanguagesAutoTrackOptions {
  AUDIO_LANGUAGE,
  SUBTITLES_LANGUAGE,
}

export type PlayerInitialLanguagesContextValue = {
  handleVideoWithProfileLanguage: () => void;
  setInitialAudioLanguage: (language: AudioOrSubsLanguage) => void;
  initialSubtitles: AudioOrSubsLanguage | null;
  initialAudioLanguage: AudioOrSubsLanguage | null;
  getInitialAutoTrack: (
    playerTracks: AudioTrack[] | TextTrack[],
    type: LanguagesAutoTrackOptions,
  ) => number;
};
