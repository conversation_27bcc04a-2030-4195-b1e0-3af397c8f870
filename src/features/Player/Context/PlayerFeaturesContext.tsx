import {
  createContext,
  FC,
  PropsWithChildren,
  useContext,
  useEffect,
  useMemo,
} from 'react';

import { useCoreHouseholdQuery } from 'services/api/newApi/core/household';
import { useConfig } from 'services/config';
import { useLogger } from 'services/logger';
import { isEmpty } from 'utils/misc/isEmpty';

import {
  PlayerFeatures,
  PlayerFeaturesContextValue,
  PlayerFeaturesProviderProps,
} from './types';
import { usePlayerErrorScreen } from './PlayerErrorContext';

const PlayerFeaturesContext = createContext<PlayerFeaturesContextValue>(
  {} as PlayerFeaturesContextValue,
);

export const PlayerFeaturesProvider: FC<
  PropsWithChildren<PlayerFeaturesProviderProps>
> = ({
  channel,
  recording,
  program,
  withConfigLagTime = false,
  withConfigLeadTime = false,
  children,
}) => {
  const { showErrorScreen, closeErrorScreen } = usePlayerErrorScreen();

  const { data: householdInfo } = useCoreHouseholdQuery();

  const {
    tveStreamDisabled,
    startOverDisabled,
    catchUpDisabled,
    recordingDisabled,
  } = program?.properties || {};

  const { logger } = useLogger();
  const {
    config: {
      appConfig: { player: playerConfig },
    },
  } = useConfig();

  // user npvr service is required for startover and scrubbing features as
  // fast-forward
  const npvrServiceActive = Boolean(
    householdInfo && householdInfo.npvr && program,
  );

  // three layers of flags:
  // - channel flags, may disable features globally for channel
  // - channel program flags, channel may allow but specific program not
  // - user npvr service, as dependency for channel and program
  const features: PlayerFeatures = useMemo((): PlayerFeatures => {
    const lagTime = withConfigLagTime ? playerConfig.videoLagTime : 0;
    const leadTime = withConfigLeadTime ? playerConfig.videoLeadTime : 0;

    if (channel && program) {
      const {
        playFeatures: { otg },
      } = channel;

      if (isEmpty(program)) {
        const disableNpvrFeaturesIfProgramNotExist = () => {
          return {
            blackout: false,
            fastForward: false,
            startOver: false,
            catchup: false,
            npvr: false,
            leadTime,
            lagTime,
          };
        };

        return disableNpvrFeaturesIfProgramNotExist();
      }

      return {
        blackout: Boolean(tveStreamDisabled || false),
        fastForward: !otg.isFastForwardBlocked && npvrServiceActive,
        startOver: otg.isStartOver && !(startOverDisabled || false),
        catchup: otg.isCatchUp && !(catchUpDisabled || false),
        npvr: otg.isNpvr && !(recordingDisabled || false) && npvrServiceActive,
        leadTime,
        lagTime,
      };
    }

    if (recording) {
      const recordingTveStreamDisabled =
        recording.properties?.tveStreamDisabled;

      // in theory, when user not have npvr feature enabled, then there
      // should not be recordings for him, but he can stil have recordings
      // created before feature was disabled, so we check user npvr flag here as well
      return {
        blackout: Boolean(recordingTveStreamDisabled),
        fastForward: npvrServiceActive,
        startOver: true,
        catchup: true,
        npvr: npvrServiceActive && !!program,
        leadTime,
        lagTime,
      };
    }

    // default player features for vod, they're not used for vod but for
    // consistency we set them to have same logic
    return {
      blackout: false,
      fastForward: true,
      startOver: true,
      catchup: false,
      npvr: false,
      leadTime,
      lagTime,
    };
  }, [
    channel,
    program,
    recording,
    npvrServiceActive,
    playerConfig,
    withConfigLagTime,
    withConfigLeadTime,
    catchUpDisabled,
    recordingDisabled,
    startOverDisabled,
    tveStreamDisabled,
  ]);

  useEffect(() => {
    if (features) {
      logger.debug('Player features', features);
    }
  }, [features, logger]);

  useEffect(() => {
    if (features.blackout) {
      showErrorScreen('PLAYER_BLACKOUT');
    } else {
      closeErrorScreen();
    }
  }, [closeErrorScreen, features.blackout, showErrorScreen]);

  return (
    <PlayerFeaturesContext.Provider value={{ features }}>
      {children}
    </PlayerFeaturesContext.Provider>
  );
};

export const usePlayerFeatures = (): PlayerFeaturesContextValue => {
  const context = useContext(PlayerFeaturesContext);

  if (context) {
    return context;
  }
  throw new Error('Component beyond PlayerFeaturesContext');
};
