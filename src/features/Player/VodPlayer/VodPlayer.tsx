import React, { FC, PropsWithChildren } from 'react';

import { BasePlayer } from 'features/Player/BasePlayer';
import {
  PlayerFeaturesProvider,
  PlayerVodProvider,
  usePlayerPlayback,
  usePlayerVod,
} from 'features/Player/Context';
import {
  useBlockUnauthorizedAccess,
  useHandlePlayInfoError,
} from 'features/Player/Hook';
import { useVodPlayInfo } from 'services/api/oldApi/vod';
import { useUserProfile } from 'services/user/UserProfileContext/UserProfileContext';
import { useVerifyVodAccess } from 'features/VOD/VodAccess';
import { useErrorScreen } from 'services/error';

import { VodPlayerProps, VodPlayerWrapperProps } from './types';
import { VodPlayerController } from './VodPlayerController';

export const VodPlayerWrapper: FC<PropsWithChildren<VodPlayerWrapperProps>> = ({
  playback,
  children,
}) => {
  const { versionExternalId, loop } = playback;
  const { reset: resetPlayback } = usePlayerPlayback();
  const { handlePlayInfoError } = useHandlePlayInfoError();
  const { userHhTech } = useUserProfile();
  const { vod } = usePlayerVod();
  const { isVodAvailable } = useVerifyVodAccess({ vod });
  const { showErrorModal } = useErrorScreen();
  const { data: playInfo, refetch: refetchPlayInfo } = useVodPlayInfo(
    { versionExternalId, hhTech: userHhTech },
    (error: unknown) => handlePlayInfoError(error, refetchPlayInfo),
  );

  if (!isVodAvailable) {
    resetPlayback();
    showErrorModal('SERVICE_NOT_AVAILABLE_ON_DEVICE');
  }

  if (!playInfo) {
    return <></>;
  }

  return (
    <PlayerFeaturesProvider>
      <BasePlayer
        mediaUrl={playInfo.streamUrl}
        casToken={playInfo.casToken}
        startPosition={playInfo.resumePosition}
        loop={loop}
        refetchToken={refetchPlayInfo}
        isVod
      >
        <VodPlayerController playback={playback}>
          {children}
        </VodPlayerController>
      </BasePlayer>
    </PlayerFeaturesProvider>
  );
};

export const VodPlayer: FC<PropsWithChildren<VodPlayerProps>> = (props) => {
  const { playback, children } = props;
  const { assetExternalId } = playback;

  useBlockUnauthorizedAccess();

  return (
    <PlayerVodProvider assetExternalId={assetExternalId}>
      <VodPlayerWrapper playback={playback}>{children}</VodPlayerWrapper>
    </PlayerVodProvider>
  );
};
