import { useEffect, useState } from 'react';

import { REFRESH_SESSION_INTERVAL } from 'services/api/oldApi/config';
import { useRefreshLiveSessionQuery } from 'services/api/newApi/live/channels';

import { useRefreshLiveSessionParams } from './types';

export const useRefreshLiveSession = ({
  channelExtId,
}: useRefreshLiveSessionParams) => {
  const [isQueryEnabled, setIsQueryEnabled] = useState(false);
  useRefreshLiveSessionQuery({ channelExtId, isQueryEnabled });

  useEffect(() => {
    const timeout = setTimeout(() => {
      setIsQueryEnabled(true);
    }, REFRESH_SESSION_INTERVAL);

    return () => {
      clearTimeout(timeout);
    };
  }, []);
};
