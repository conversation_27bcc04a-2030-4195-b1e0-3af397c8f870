import { PlayerMode, PlayerState } from 'features/Player/types';
import {
  AssetId,
  ChannelId,
  ParentControlLevel,
  ProgramId,
  RecordingId,
  TimeInSeconds,
  Timestamp,
  VersionId,
} from 'services/api/common/types';

import { PlayerPlayback } from '../Context';

export type TimerType = 'seconds' | 'timestamp';

export type PlayContentPlayerParams = {
  muted?: boolean;
  mode?: PlayerMode;
  programParentalControlLevel?: ParentControlLevel;
};

export type PlayTrailerContentParams = PlayContentPlayerParams & {
  assetExternalId: AssetId;
  startPosition?: TimeInSeconds;
};

export type PlayVodContentParams = PlayContentPlayerParams & {
  assetExternalId: AssetId;
  versionExternalId: VersionId;
  startPosition?: TimeInSeconds;
};

export type PlayChannelParams = PlayContentPlayerParams & {
  channelExtId: ChannelId;
  programExtId?: ProgramId;
  isFromRecentlyWatched?: boolean;
};

export type PlayRecordingParams = PlayContentPlayerParams & {
  channelExtId: ChannelId;
  recordingExtId: RecordingId;
  startPosition?: TimeInSeconds;
};

export type PlayCatchupParams = PlayChannelParams & {
  programExtId: ProgramId;
  startPosition?: TimeInSeconds;
  programEndDate: TimeInSeconds;
};

export type PlayStartoverParams = PlayChannelParams & {
  programExtId: ProgramId;
  startPosition?: TimeInSeconds;
  channelExtId: ChannelId;
};

export interface usePlayContentValue {
  playTrailer: (params: PlayTrailerContentParams) => void;
  playVod: (params: PlayVodContentParams) => void;
  playRecording: (params: PlayRecordingParams) => void;
  playChannel: (params: PlayChannelParams) => void;
  playCatchup: (params: PlayCatchupParams) => void;
  playStartover: (params: PlayStartoverParams) => void;
}

export interface useTerminalValue {
  registerTerminal: ({
    onSuccessTerminalDeleteCallback,
  }: {
    onSuccessTerminalDeleteCallback?: () => void;
  }) => Promise<void>;
  addNewDevice: () => Promise<void>;
}

export interface useBlockUnauthorizedAccessValue {
  isAvailable: boolean;
}

export interface useHandlePlayInfoErrorParamsValue {
  handlePlayInfoError: (
    error: unknown,
    refetchCallback: RefetchCallback,
  ) => void;
}

export interface useRefreshLiveSessionParams {
  channelExtId: ChannelId;
}

export interface useSessionWatcherParams {
  onSessionClose: () => void;
  disable?: boolean;
}
export interface useGenericContinueWatchingHandlerParams {
  handler: () => void;
  onPlaybackStates?: PlayerState[];
  onUnmount: boolean;
}

export interface useProgramEndEventParams {
  timerType: TimerType;
}

export interface useProgramTimerParams {
  startTimeUtc: Timestamp;
  endTimeUtc: Timestamp;
}

export interface RegisterTerminalParams {
  onSuccessTerminalDeleteCallback?: () => void;
}

export interface UseChannelBasedPlaybackSessionControllerParams {
  playback: PlayerPlayback;
}

export type RefetchCallback = () => void;
