import { useCallback, useMemo } from 'react';

import { usePlayerTimer } from 'features/Player/BasePlayer/Context';
import {
  isInitialPlayback,
  usePlayerPlayback,
  usePlayerProgram,
} from 'features/Player/Context';
import { PlayerState, PlayerType } from 'features/Player/types';
import { CatchupService, StartOverService } from 'services/api/common/types';
import { useRecentlyWatchedLiveMutation } from 'services/api/oldApi/myzone';
import { useRecentlyWatchedChannels } from 'services/localApi/RecentlyWatchedChannelsApiContext/RecentlyWatchedChannelsApiContext';
import { useLogger } from 'services/logger';
import { isEmpty } from 'utils/misc/isEmpty';
import { useCoreHouseholdQuery } from 'services/api/newApi/core/household';
import { useAuthenticationStatus } from 'services/user';

import { useGenericContinueWatchingHandler } from './useGenericContinueWatchingHandler';
import { useProgramTimer } from './useProgramTimer';

import { usePlayerSettingsValues } from '../Interface/SettingsMenu';

export const useLiveContinueWatchingHandler = () => {
  const { logger } = useLogger();
  const { playback } = usePlayerPlayback();
  const { program, channel } = usePlayerProgram();
  const { timer } = usePlayerTimer();
  const { mutate } = useRecentlyWatchedLiveMutation();
  const { activeAudioLanguage, activeSubtitles } = usePlayerSettingsValues();

  const { startTimeUtc, endTimeUtc } = program;
  const { addChannelToRecentlyWatched } = useRecentlyWatchedChannels();
  const { isAuthenticated } = useAuthenticationStatus();
  const { data: householdInfo } = useCoreHouseholdQuery(isAuthenticated);

  const { getContinueWatchingPosition } = useProgramTimer({
    startTimeUtc,
    endTimeUtc,
  });

  const timeShiftingService = useMemo(():
    | CatchupService
    | StartOverService
    | '' => {
    if (!isInitialPlayback(playback)) {
      switch (playback.type) {
        case PlayerType.Catchup:
          return 'catchup';
        case PlayerType.Startover:
          return 'stov';
        default:
          return '';
      }
    }

    return '';
  }, [playback]);

  const addProgramToRecentlyWatched = useCallback(() => {
    if (isEmpty(program)) return;

    if (householdInfo) {
      const { audioLang, subsLang } = householdInfo;
      const { channelExtId } = channel;
      const { programExtId } = program;

      const isCatchupForProgramUnavailable =
        !channel.playFeatures.otg.isCatchUp ||
        program.properties?.catchUpDisabled;
      if (isCatchupForProgramUnavailable) {
        return;
      }

      const resumePosition = getContinueWatchingPosition(timer.currentTime);
      if (resumePosition === 0) {
        return;
      }

      logger.debug('Adding to continue watching:', {
        channelExtId,
        programExtId,
        resumePosition,
        audioLang,
        subsLang,
        timeShiftingService,
      });

      mutate({
        channelExternalId: channelExtId,
        programExternalId: programExtId,
        resumePosition,
        timeShiftingService,
      });
    }
  }, [
    householdInfo,
    channel,
    getContinueWatchingPosition,
    logger,
    mutate,
    program,
    timeShiftingService,
    timer.currentTime,
  ]);

  const onContinueWatching = () => {
    addProgramToRecentlyWatched();
    addChannelToRecentlyWatched({
      audioLang: activeAudioLanguage,
      chanImage: channel.logoSignature,
      chanTitle: channel.name,
      channelExtId: channel.channelExtId,
      creationTimestamp: new Date().getTime(),
      householdExtId: householdInfo?.householdExtId || '',
      subsLang: activeSubtitles,
    });
  };

  useGenericContinueWatchingHandler({
    handler: onContinueWatching,
    onPlaybackStates: [PlayerState.Pause],
    onUnmount: true,
  });
};
