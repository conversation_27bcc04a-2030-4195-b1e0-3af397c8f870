import { useEffect } from 'react';

import { usePlayerPlayback } from 'features/Player/Context';
import { useErrorScreen } from 'services/error';
import { useLogger } from 'services/logger';
import { useAuthenticationStatus } from 'services/user';

import { useBlockUnauthorizedAccessValue } from './types';

export const useBlockUnauthorizedAccess = (
  allowFlag?: boolean,
): useBlockUnauthorizedAccessValue => {
  const { logger } = useLogger();
  const { showErrorModal } = useErrorScreen();
  const { reset: resetPlayback } = usePlayerPlayback();
  const { isAuthenticated } = useAuthenticationStatus();

  const isAvailable = Boolean(isAuthenticated || allowFlag);

  useEffect(() => {
    if (!isAvailable) {
      logger.info('User is not authenticated and cant watch this content');
      resetPlayback();

      showErrorModal('NO_AUTHORIZATION');
    }
  }, [isAvailable, logger, resetPlayback, showErrorModal]);

  return { isAvailable };
};
