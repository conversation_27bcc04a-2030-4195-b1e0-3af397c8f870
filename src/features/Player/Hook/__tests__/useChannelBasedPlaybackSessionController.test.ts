import { vi, expect } from 'vitest';

import { PlayerType } from 'features/Player/types';
import { act, renderHook, waitFor } from 'utils/testing/customRender';
import { setupMockServer } from 'services/api/mock/mock.server';
import { closeLiveSessionMockHandlersWithSpy } from 'services/api/newApi/live/channels/mocks/closeLiveSession.mock';
import { refreshLiveSessionMockHandlersWithSpy } from 'services/api/newApi/live/channels/mocks/refreshLiveSession.mock';

import { UseChannelBasedPlaybackSessionControllerParams } from '../types';
import { useChannelBasedPlaybackSessionController } from '../useChannelBasedPlaybackSessionController';

const CHANNEL_A_ID = '11111';
const CHANNEL_B_ID = '22222';

const LIVE_CHANNEL_A_PROPS: UseChannelBasedPlaybackSessionControllerParams = {
  playback: { type: PlayerType.Channel, channelExtId: CHANNEL_A_ID },
};

const LIVE_CHANNEL_B_PROPS: UseChannelBasedPlaybackSessionControllerParams = {
  playback: { type: PlayerType.Channel, channelExtId: CHANNEL_B_ID },
};

const STARTOVER_CHANNEL_A_PROPS: UseChannelBasedPlaybackSessionControllerParams =
  {
    playback: {
      type: PlayerType.Startover,
      channelExtId: CHANNEL_A_ID,
      programExtId: '',
    },
  };

const STARTOVER_CHANNEL_B_PROPS: UseChannelBasedPlaybackSessionControllerParams =
  {
    playback: {
      type: PlayerType.Startover,
      channelExtId: CHANNEL_B_ID,
      programExtId: '',
    },
  };

const CATCHUP_CHANNEL_A_PROPS: UseChannelBasedPlaybackSessionControllerParams =
  {
    playback: {
      type: PlayerType.Catchup,
      channelExtId: CHANNEL_A_ID,
      programExtId: '',
    },
  };

const RECORDING_CHANNEL_A_PROPS: UseChannelBasedPlaybackSessionControllerParams =
  {
    playback: {
      type: PlayerType.Recording,
      channelExtId: CHANNEL_A_ID,
      recordingExtId: '',
    },
  };

const CATCHUP_CHANNEL_B_PROPS: UseChannelBasedPlaybackSessionControllerParams =
  {
    playback: {
      type: PlayerType.Catchup,
      channelExtId: CHANNEL_B_ID,
      programExtId: '',
    },
  };

const RECORDING_CHANNEL_B_PROPS: UseChannelBasedPlaybackSessionControllerParams =
  {
    playback: {
      type: PlayerType.Catchup,
      channelExtId: CHANNEL_B_ID,
      programExtId: '',
    },
  };

const VOD_PROPS: UseChannelBasedPlaybackSessionControllerParams = {
  playback: {
    type: PlayerType.Vod,
    assetExternalId: '',
    versionExternalId: '',
  },
};

const TRAILER_PROPS: UseChannelBasedPlaybackSessionControllerParams = {
  playback: {
    type: PlayerType.Trailer,
    assetExternalId: '',
  },
};

describe('useChannelBasedPlaybackSessionController', () => {
  const spyFunc = vi.fn();
  const server = setupMockServer(
    ...closeLiveSessionMockHandlersWithSpy(spyFunc, CHANNEL_A_ID),
    ...refreshLiveSessionMockHandlersWithSpy(spyFunc, CHANNEL_A_ID),
  );

  afterEach(() => {
    spyFunc.mockReset();
  });

  const testCasesForCloseSession = [
    {
      name: 'live channel to different one live channel',
      initialProps: LIVE_CHANNEL_A_PROPS,
      newProps: LIVE_CHANNEL_B_PROPS,
    },
    {
      name: 'live channel to different one startover channel',
      initialProps: LIVE_CHANNEL_A_PROPS,
      newProps: STARTOVER_CHANNEL_B_PROPS,
    },
    {
      name: 'live channel to different one catchup channel',
      initialProps: LIVE_CHANNEL_A_PROPS,
      newProps: CATCHUP_CHANNEL_B_PROPS,
    },
    {
      name: 'live channel to different one recording channel',
      initialProps: LIVE_CHANNEL_A_PROPS,
      newProps: RECORDING_CHANNEL_B_PROPS,
    },
    {
      name: 'live channel to vod',
      initialProps: LIVE_CHANNEL_A_PROPS,
      newProps: VOD_PROPS,
    },
    {
      name: 'live channel to trailer',
      initialProps: LIVE_CHANNEL_A_PROPS,
      newProps: TRAILER_PROPS,
    },
  ];

  testCasesForCloseSession.forEach(({ name, initialProps, newProps }) => {
    it(`should close session on switch from ${name}`, async () => {
      const { rerender } = renderHook(
        (props: UseChannelBasedPlaybackSessionControllerParams) =>
          useChannelBasedPlaybackSessionController(props),
        { initialProps },
      );

      expect(spyFunc).toBeCalledTimes(0);

      act(() => {
        rerender(newProps);
      });

      await waitFor(() => expect(spyFunc).toBeCalledTimes(1));
      await waitFor(() => expect(spyFunc).not.toBeCalledTimes(2));
    });
  });

  it('should refresh session correctly while switching between different playback but for the same channel', async () => {
    const { rerender } = renderHook(
      (props: UseChannelBasedPlaybackSessionControllerParams) =>
        useChannelBasedPlaybackSessionController(props),
      { initialProps: LIVE_CHANNEL_A_PROPS },
    );

    expect(spyFunc).toBeCalledTimes(0);

    act(() => {
      rerender(STARTOVER_CHANNEL_A_PROPS);
    });

    await waitFor(() => expect(spyFunc).toBeCalledTimes(1));
    await waitFor(() => expect(spyFunc).not.toBeCalledTimes(2));

    act(() => {
      rerender(CATCHUP_CHANNEL_A_PROPS);
    });

    await waitFor(() => expect(spyFunc).toBeCalledTimes(2));
    await waitFor(() => expect(spyFunc).not.toBeCalledTimes(3));

    act(() => {
      rerender(RECORDING_CHANNEL_A_PROPS);
    });

    await waitFor(() => expect(spyFunc).toBeCalledTimes(3));
    await waitFor(() => expect(spyFunc).not.toBeCalledTimes(4));

    act(() => {
      rerender(LIVE_CHANNEL_A_PROPS);
    });

    await waitFor(() => expect(spyFunc).toBeCalledTimes(4));
    await waitFor(() => expect(spyFunc).not.toBeCalledTimes(5));
  });
});
