import { useCallback, useEffect, useRef } from 'react';

import {
  useCloseLiveSessionMutation,
  useRefreshLiveSessionMutation,
} from 'services/api/newApi/live/channels';

import { UseChannelBasedPlaybackSessionControllerParams } from './types';

import { isChannelBasedPlayback, isInitialPlayback } from '../Context';
import { PlayerType } from '../types';

export const useChannelBasedPlaybackSessionController = ({
  playback,
}: UseChannelBasedPlaybackSessionControllerParams) => {
  const { mutate: closeLiveSession } = useCloseLiveSessionMutation();
  const { mutate: refreshLiveSession } = useRefreshLiveSessionMutation();

  const lastPlayedchannelExtId = useRef<string>();

  const closeLiveSessionForLastPlayedChannel = useCallback(() => {
    if (lastPlayedchannelExtId.current) {
      closeLiveSession({
        channelExtId: lastPlayedchannelExtId.current,
      });
    }
  }, [closeLiveSession, lastPlayedchannelExtId]);

  useEffect(() => {
    const isClosedPlayerNow = isInitialPlayback(playback);
    const wasChannelBasedContentPlayedBefore = Boolean(lastPlayedchannelExtId);

    try {
      if (isClosedPlayerNow && wasChannelBasedContentPlayedBefore) {
        closeLiveSessionForLastPlayedChannel();
        return;
      }

      if (isClosedPlayerNow) {
        return;
      }

      switch (playback.type) {
        case PlayerType.Trailer:
        case PlayerType.Vod:
          if (wasChannelBasedContentPlayedBefore) {
            closeLiveSessionForLastPlayedChannel();
          }
          break;
        case PlayerType.Channel:
        case PlayerType.Recording:
        case PlayerType.Catchup:
        case PlayerType.Startover:
          const isPlaybackSwitchedToDifferentChannel =
            lastPlayedchannelExtId.current !== playback.channelExtId;
          const isPlaybackSwitchedToTheSameChannel =
            lastPlayedchannelExtId.current === playback.channelExtId;

          if (isPlaybackSwitchedToDifferentChannel) {
            closeLiveSessionForLastPlayedChannel();
            break;
          }

          if (isPlaybackSwitchedToTheSameChannel) {
            refreshLiveSession({
              channelExtId: playback.channelExtId,
            });
            break;
          }
          break;
        default:
          throw new Error('Invalid playback type');
      }
    } finally {
      if (isChannelBasedPlayback(playback)) {
        lastPlayedchannelExtId.current = playback.channelExtId;
      } else {
        lastPlayedchannelExtId.current = undefined;
      }
    }
  }, [closeLiveSessionForLastPlayedChannel, playback, refreshLiveSession]);
};
