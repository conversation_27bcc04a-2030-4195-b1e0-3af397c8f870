import { useCallback } from 'react';
import { millisecondsToSeconds } from 'date-fns';

import { useCoreHouseholdQuery } from 'services/api/newApi/core/household';
import { PlayerPlayback, usePlayerPlayback } from 'features/Player/Context';
import { usePlayerInitialLanguages } from 'features/Player/Context/PlayerInitialLanguagesContext';
import { PlayerMode, PlayerType } from 'features/Player/types';
import { useAppLayoutMode } from 'services/appLayoutMode';
import { useGlobalLoaderContext } from 'services/loader';
import { useAuthenticationStatus } from 'services/user';
import { useModalPrControl } from 'services/user/ModalPrControl';
import { useGetVodParentalControlMutation } from 'services/api/oldApi/vod';
import { checkIfIsParentalPinRequired } from 'services/user/ModalPrControl/helper';
import { useRegionalTvContext } from 'services/regionalTv';
import { useRouteOrigin } from 'containers/Navigation/context';
import { useGetProgramParentalControlMutation } from 'services/api/newApi/core/tvguide';
import { useGetRecordingParentalControlMutation } from 'services/api/newApi/optional/npvr';

import {
  PlayCatchupParams,
  PlayChannelParams,
  PlayRecordingParams,
  PlayStartoverParams,
  PlayTrailerContentParams,
  PlayVodContentParams,
  usePlayContentValue,
} from './types';
import { useProgramForChannel } from './helpers';

import { usePlayerTimeChannelLimit } from '../BasePlayer/Context';
import { LAG_TIME } from '../Context/constants';

export const usePlayContent = (): usePlayContentValue => {
  const { isAuthenticated } = useAuthenticationStatus();
  const { data: householdInfo } = useCoreHouseholdQuery(isAuthenticated);
  const { showPrControlModal } = useModalPrControl();
  const { setMode, mode: currentMode, setIsSearchOpen } = useAppLayoutMode();
  const { playback: currentPlayback, setPlayback } = usePlayerPlayback();
  const { setIsLoaderVisible } = useGlobalLoaderContext();
  const { updateChannelTimer, clearChannelTimer } = usePlayerTimeChannelLimit();
  const { mutateAsync: getRecordingParentalControlLevel } =
    useGetRecordingParentalControlMutation();
  const { mutateAsync: getChannelProgramParentalControlLevel } =
    useGetProgramParentalControlMutation();
  const { mutateAsync: getVodParentalControlLevel } =
    useGetVodParentalControlMutation();
  const { getProgramForChannel } = useProgramForChannel();
  const { setIsRegionalSelectViewVisible } = useRegionalTvContext();
  const { handleVideoWithProfileLanguage } = usePlayerInitialLanguages();
  const { handlePlayerRoute } = useRouteOrigin();

  const prepare = useCallback(
    (playback: PlayerPlayback, mode?: PlayerMode) => {
      setIsSearchOpen(false);
      handlePlayerRoute();
      const newMode =
        mode ||
        (currentMode === PlayerMode.Mini
          ? PlayerMode.Mini
          : PlayerMode.Expanded);

      const isDifferentPlayback =
        Object.entries(currentPlayback).toString() !==
        Object.entries(playback).toString();
      // do not update playback when try to set to same playback
      if (isDifferentPlayback) {
        setPlayback(playback);
        setIsLoaderVisible(true);
      }
      setIsRegionalSelectViewVisible(false);
      setMode(newMode);
    },
    [
      setIsSearchOpen,
      handlePlayerRoute,
      currentMode,
      currentPlayback,
      setIsRegionalSelectViewVisible,
      setMode,
      setPlayback,
      setIsLoaderVisible,
    ],
  );

  const playTrailer = useCallback(
    async (params: PlayTrailerContentParams) => {
      const {
        assetExternalId,
        startPosition,
        muted,
        mode,
        programParentalControlLevel,
      } = params;
      const play = () => {
        handleVideoWithProfileLanguage();

        prepare(
          {
            type: PlayerType.Trailer,
            assetExternalId,
            startPosition,
            muted,
          },
          mode,
        );
      };

      let prLevel = programParentalControlLevel;
      if (!programParentalControlLevel) {
        prLevel = await getVodParentalControlLevel({
          assetExternalId,
        });
      }

      if (checkIfIsParentalPinRequired(householdInfo, prLevel)) {
        showPrControlModal(play);
        return;
      }
      play();
    },
    [
      householdInfo,
      handleVideoWithProfileLanguage,
      prepare,
      getVodParentalControlLevel,
      showPrControlModal,
    ],
  );

  const playVod = useCallback(
    async (params: PlayVodContentParams) => {
      const {
        assetExternalId,
        versionExternalId,
        startPosition,
        muted,
        mode,
        programParentalControlLevel,
      } = params;
      const play = () => {
        clearChannelTimer();
        handleVideoWithProfileLanguage();

        prepare(
          {
            type: PlayerType.Vod,
            assetExternalId,
            versionExternalId,
            startPosition,
            muted,
          },
          mode,
        );
      };

      let prLevel = programParentalControlLevel;
      if (!programParentalControlLevel) {
        prLevel = await getVodParentalControlLevel({
          assetExternalId,
        });
      }

      if (checkIfIsParentalPinRequired(householdInfo, prLevel)) {
        showPrControlModal(play);
        return;
      }
      play();
    },
    [
      householdInfo,
      clearChannelTimer,
      handleVideoWithProfileLanguage,
      prepare,
      getVodParentalControlLevel,
      showPrControlModal,
    ],
  );

  const playRecording = useCallback(
    async (params: PlayRecordingParams) => {
      const {
        channelExtId,
        recordingExtId,
        startPosition,
        muted,
        mode,
        programParentalControlLevel,
      } = params;
      const play = () => {
        clearChannelTimer();
        handleVideoWithProfileLanguage();

        prepare(
          {
            type: PlayerType.Recording,
            channelExtId,
            recordingExtId,
            startPosition,
            muted,
          },
          mode,
        );
      };

      let prLevel = programParentalControlLevel;
      if (!programParentalControlLevel) {
        prLevel = await getRecordingParentalControlLevel({
          recordingExtId: recordingExtId,
        });
      }

      if (checkIfIsParentalPinRequired(householdInfo, prLevel)) {
        showPrControlModal(play);
        return;
      }

      play();
    },
    [
      householdInfo,
      clearChannelTimer,
      handleVideoWithProfileLanguage,
      prepare,
      getRecordingParentalControlLevel,
      showPrControlModal,
    ],
  );

  const playChannel = useCallback(
    async (params: PlayChannelParams) => {
      const {
        channelExtId,
        programExtId,
        muted,
        mode,
        programParentalControlLevel,
      } = params;

      let programId = programExtId;

      if (!programExtId) {
        programId = getProgramForChannel(channelExtId)?.programExtId;
      }

      const play = () => {
        updateChannelTimer(channelExtId);
        handleVideoWithProfileLanguage();

        prepare({ type: PlayerType.Channel, channelExtId, muted }, mode);
      };

      let prLevel = programParentalControlLevel;
      if (!programParentalControlLevel && programId) {
        prLevel = await getChannelProgramParentalControlLevel({
          programExtId: programId,
        });
      }

      if (checkIfIsParentalPinRequired(householdInfo, prLevel)) {
        showPrControlModal(play);
        return;
      }
      play();
    },
    [
      householdInfo,
      getProgramForChannel,
      updateChannelTimer,
      handleVideoWithProfileLanguage,
      prepare,
      getChannelProgramParentalControlLevel,
      showPrControlModal,
    ],
  );

  const playCatchup = useCallback(
    async (params: PlayCatchupParams) => {
      const {
        channelExtId,
        programExtId,
        startPosition,
        muted,
        mode,
        programEndDate,
        programParentalControlLevel,
      } = params;

      const play = () => {
        const isDynamicCatchup =
          programEndDate + LAG_TIME >
          millisecondsToSeconds(new Date().getTime());

        clearChannelTimer();
        handleVideoWithProfileLanguage();

        if (isDynamicCatchup) {
          prepare(
            {
              type: PlayerType.Startover,
              channelExtId,
              programExtId,
              startPosition,
              muted,
            },
            mode,
          );
          return;
        }

        prepare(
          {
            type: PlayerType.Catchup,
            channelExtId,
            programExtId,
            startPosition,
            muted,
          },
          mode,
        );
      };

      let prLevel = programParentalControlLevel;
      const programId = programExtId;
      if (!programParentalControlLevel) {
        prLevel = await getChannelProgramParentalControlLevel({
          programExtId: programId,
        });
      }

      if (checkIfIsParentalPinRequired(householdInfo, prLevel)) {
        showPrControlModal(play);
        return;
      }
      play();
    },
    [
      householdInfo,
      clearChannelTimer,
      handleVideoWithProfileLanguage,
      prepare,
      getChannelProgramParentalControlLevel,
      showPrControlModal,
    ],
  );

  const playStartover = useCallback(
    async (params: PlayStartoverParams) => {
      const {
        channelExtId,
        programExtId,
        startPosition,
        muted,
        mode,
        programParentalControlLevel,
      } = params;

      const play = () => {
        updateChannelTimer(channelExtId);
        handleVideoWithProfileLanguage();
        prepare(
          {
            type: PlayerType.Startover,
            channelExtId,
            programExtId,
            startPosition,
            muted,
          },
          mode,
        );
      };

      let prLevel = programParentalControlLevel;
      const programId = programExtId;
      if (!programParentalControlLevel) {
        prLevel = await getChannelProgramParentalControlLevel({
          programExtId: programId,
        });
      }

      if (checkIfIsParentalPinRequired(householdInfo, prLevel)) {
        showPrControlModal(play);
        return;
      }
      play();
    },
    [
      householdInfo,
      updateChannelTimer,
      handleVideoWithProfileLanguage,
      prepare,
      getChannelProgramParentalControlLevel,
      showPrControlModal,
    ],
  );

  return {
    playTrailer,
    playVod,
    playRecording,
    playChannel,
    playCatchup,
    playStartover,
  };
};
