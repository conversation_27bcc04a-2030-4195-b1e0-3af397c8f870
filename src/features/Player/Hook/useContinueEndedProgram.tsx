import { useCallback, useEffect } from 'react';
import { secondsToMilliseconds } from 'date-fns';

import { usePlayerState } from 'features/Player/BasePlayer/Context';
import {
  CatchupPlayback,
  usePlayerFeatures,
  usePlayerPlayback,
  usePlayerProgram,
} from 'features/Player/Context';
import { PlayerState, PlayerType } from 'features/Player/types';
import { useLogger } from 'services/logger';
import { ScheduleProgram } from 'services/api/newApi/core/tvguide';
import { useModalPrControl } from 'services/user/ModalPrControl';
import { useAuthenticationStatus } from 'services/user';
import { checkIfIsParentalPinRequired } from 'services/user/ModalPrControl/helper';
import { useCoreHouseholdQuery } from 'services/api/newApi/core/household';

import { PlayerOverlayContent } from '../PlayerOverlay/types';
import { usePlayerOverlay } from '../PlayerOverlay/PlayerOverlayContext';

export const useContinueEndedContent = (playerType: PlayerType) => {
  const { logger } = useLogger();
  const { setPlayback, reset } = usePlayerPlayback();
  const { state: currentPlayerState } = usePlayerState();
  const { showPrControlModal } = useModalPrControl();
  const { isAuthenticated } = useAuthenticationStatus();
  const { data: householdInfo } = useCoreHouseholdQuery(isAuthenticated);
  const { setPlayerOverlay } = usePlayerOverlay();
  const { getNextProgram, getCurrentLiveProgram } = usePlayerProgram();
  const {
    features: { lagTime },
  } = usePlayerFeatures();

  const playNextStartover = useCallback(
    (nextProgramInSchedule: ScheduleProgram) => {
      logger.debug('Jump to startover after catchup');
      setPlayback((currentPlayback) => {
        const { type, programExtId, startPosition, ...rest } =
          currentPlayback as CatchupPlayback;
        return {
          type: PlayerType.Startover,
          programExtId: nextProgramInSchedule.programExtId,
          startPosition: lagTime,
          ...rest,
        };
      });
    },
    [lagTime, logger, setPlayback],
  );

  const playNextCatchup = useCallback(
    (nextProgramInSchedule: ScheduleProgram) => {
      logger.debug('Jump to next catchup in schedule');
      setPlayback((currentPlayback) => {
        const { type, programExtId, startPosition, ...rest } =
          currentPlayback as CatchupPlayback;
        return {
          type: PlayerType.Catchup,
          programExtId: nextProgramInSchedule.programExtId,
          startPosition: lagTime,
          ...rest,
        };
      });
    },
    [lagTime, logger, setPlayback],
  );

  const playLiveAsNextProgram = useCallback(() => {
    logger.debug('Jump to live program');
    const currentLiveProgram = getCurrentLiveProgram();
    const isParentalControlPinRequired = checkIfIsParentalPinRequired(
      householdInfo,
      currentLiveProgram?.prLevel,
    );

    isParentalControlPinRequired
      ? setPlayerOverlay(PlayerOverlayContent.BackToLiveWithParentalControl)
      : setPlayerOverlay(PlayerOverlayContent.BackToLiveModal);
  }, [householdInfo, getCurrentLiveProgram, logger, setPlayerOverlay]);

  const playNextProgramInSchedule = useCallback(
    (nextProgramInSchedule: ScheduleProgram) => {
      const nextProgramIsStartover =
        secondsToMilliseconds(nextProgramInSchedule.endTimeUtc) >
        new Date().getTime();
      const canPlayStartover =
        nextProgramIsStartover &&
        !nextProgramInSchedule.properties?.startOverDisabled;
      const canPlayCatchup = !nextProgramInSchedule.properties?.catchUpDisabled;
      const isNextProgramNotAvailable = !canPlayCatchup && !canPlayStartover;

      if (isNextProgramNotAvailable) {
        return playLiveAsNextProgram();
      }

      const play = () => {
        if (canPlayStartover) {
          return playNextStartover(nextProgramInSchedule);
        }
        if (canPlayCatchup && !nextProgramIsStartover) {
          return playNextCatchup(nextProgramInSchedule);
        }
        return playLiveAsNextProgram();
      };

      const isParentalControlPinRequired = checkIfIsParentalPinRequired(
        householdInfo,
        nextProgramInSchedule?.prLevel,
      );
      if (isParentalControlPinRequired) {
        return showPrControlModal(play, reset);
      }
      play();
    },
    [
      householdInfo,
      playLiveAsNextProgram,
      playNextCatchup,
      playNextStartover,
      reset,
      showPrControlModal,
    ],
  );

  const loadNextProgram = useCallback(() => {
    const nextProgramInSchedule = getNextProgram();
    if (nextProgramInSchedule) {
      return playNextProgramInSchedule(nextProgramInSchedule);
    }
    playLiveAsNextProgram();
  }, [getNextProgram, playLiveAsNextProgram, playNextProgramInSchedule]);

  useEffect(() => {
    if (currentPlayerState === PlayerState.Ended) {
      loadNextProgram();
    }
  }, [loadNextProgram, currentPlayerState]);
};
