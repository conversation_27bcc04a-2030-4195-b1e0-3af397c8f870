import { FC, PropsWithChildren } from 'react';

import { BasePlayer } from 'features/Player/BasePlayer';
import {
  PlayerFeaturesProvider,
  PlayerProgramProvider,
  usePlayerProgram,
} from 'features/Player/Context';
import { useHandlePlayInfoError, useProgramTimer } from 'features/Player/Hook';
import { useChannelPlayInfo } from 'services/api/newApi/live/channels';
import { useRegionalTvContext } from 'services/regionalTv';
import { useConfig } from 'services/config';

import { ChannelPlayerController } from './ChannelPlayerController';
import {
  ChannelPlayerInnerWrapperProps,
  ChannelPlayerProps,
  ChannelPlayerWrapperProps,
} from './types';

export const ChannelPlayerInnerWrapper: FC<
  PropsWithChildren<ChannelPlayerInnerWrapperProps>
> = ({ playback, playInfo, program, children, refetchToken }) => {
  const { loop } = playback;
  const { startTimeUtc, endTimeUtc } = program;
  const { startPosition } = useProgramTimer({
    startTimeUtc,
    endTimeUtc,
  });

  return (
    <BasePlayer
      mediaUrl={playInfo.streamUrl}
      casToken={playInfo.casToken}
      startPosition={startPosition}
      loop={loop}
      refetchToken={refetchToken}
      isLiveStream
    >
      <ChannelPlayerController playback={playback}>
        {children}
      </ChannelPlayerController>
    </BasePlayer>
  );
};

export const ChannelPlayerWrapper: FC<
  PropsWithChildren<ChannelPlayerWrapperProps>
> = ({ playback, children }) => {
  const { channelExtId } = playback;
  const { userRegionalTv } = useRegionalTvContext();
  const { config } = useConfig();
  const { program, channel } = usePlayerProgram();
  const { handlePlayInfoError } = useHandlePlayInfoError();

  const {
    appConfig: {
      techConfig: { useOptionalRegionParam },
    },
  } = config;

  const { data: playInfo, refetch: refetchPlayInfo } = useChannelPlayInfo(
    {
      channelExtId,
      isRegionalTv: channel.isRegionalTv,
      userRegion: userRegionalTv?.value,
      useOptionalRegionParam,
    },
    true,
    (error: unknown) => handlePlayInfoError(error, refetchPlayInfo),
  );

  if (!playInfo) {
    return <></>;
  }

  return (
    <PlayerFeaturesProvider channel={channel} program={program}>
      <ChannelPlayerInnerWrapper
        playback={playback}
        playInfo={playInfo}
        program={program}
        refetchToken={refetchPlayInfo}
      >
        {children}
      </ChannelPlayerInnerWrapper>
    </PlayerFeaturesProvider>
  );
};

export const ChannelPlayer: FC<PropsWithChildren<ChannelPlayerProps>> = ({
  playback,
  children,
}) => {
  const { channelExtId } = playback;

  return (
    <PlayerProgramProvider channelId={channelExtId}>
      <ChannelPlayerWrapper playback={playback}>
        {children}
      </ChannelPlayerWrapper>
    </PlayerProgramProvider>
  );
};
