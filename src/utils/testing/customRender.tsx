import React, { FC, ReactElement } from 'react';
import {
  render,
  renderHook,
  RenderHookOptions,
  RenderOptions,
} from '@testing-library/react';
import { BrowserRouter as Router } from 'react-router-dom';
import { vi } from 'vitest';

import { AppProviders } from 'containers/App';
import { MockLogger } from 'services/logger';
import { ApplicationThemeProvider } from 'theme';
import { UserThemeProvider } from 'theme/UserThemeContext';
import { useConfig } from 'services/config';
import { useCoreHouseholdQuery } from 'services/api/newApi/core/household';

const sendMock = vi.fn();
export const mockLogger = new MockLogger(sendMock);

const Providers: FC<React.PropsWithChildren<unknown>> = ({ children }) => {
  return (
    <UserThemeProvider>
      <ApplicationThemeProvider>
        <Router>
          <AppProviders logger={mockLogger}>{children}</AppProviders>
        </Router>
      </ApplicationThemeProvider>
    </UserThemeProvider>
  );
};

const TestLoader: FC<React.PropsWithChildren<unknown>> = ({ children }) => {
  const {
    config: { appConfig },
  } = useConfig();
  const { data: householdInfo } = useCoreHouseholdQuery(true);

  if (!appConfig || !householdInfo) {
    return <div data-testid='custom-render-test-loader' />;
  }
  return children;
};

const ProvidersWithLoader: FC<React.PropsWithChildren<unknown>> = ({
  children,
}) => {
  return (
    <Providers>
      <TestLoader>{children}</TestLoader>
    </Providers>
  );
};

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>,
) => render(ui, { wrapper: Providers, ...options });

const customRenderWithLoader = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>,
) => {
  return render(ui, { wrapper: ProvidersWithLoader, ...options });
};

const customHookRender = <P, R>(
  renderElement: (initialProps: P) => R,
  options?: Omit<RenderHookOptions<P>, 'wrapper'>,
) => renderHook(renderElement, { wrapper: Providers, ...options });

export * from '@testing-library/react';

export {
  customRender as render,
  customHookRender as renderHook,
  customRenderWithLoader,
};
