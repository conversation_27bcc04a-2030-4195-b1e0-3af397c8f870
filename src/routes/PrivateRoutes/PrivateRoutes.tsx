// SZYBKIE ROZWIĄZANIE - zaktualizuj PrivateRoutes.tsx

import { FC, useEffect, useRef } from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';

import { routes } from 'routes/routes-map';
import { useConfig } from 'services/config';
import { useAuthenticationStatus } from 'services/user';

export const PrivateRoutes: FC = () => {
  const { isAuthenticated } = useAuthenticationStatus();
  const location = useLocation();
  const { getTechConfig } = useConfig();
  const techConfig = getTechConfig();

  // Dodaj ref do śledzenia ostatniego przekierowania
  const lastRedirectRef = useRef<string>('');
  const redirectCountRef = useRef(0);

  const isVodRouteForbidden = !techConfig.mainTabs?.isVodTabVisible;
  const isNpvrRouteForbidden = !techConfig.withVisibleNpvrFeatures;

  console.log('[PrivateRoutes] Checking access', {
    timestamp: new Date().toISOString(),
    isAuthenticated,
    currentPath: location.pathname,
    isVodRouteForbidden,
    isNpvrRouteForbidden,
  });

  // Reset licznika jeśli zmienił się pathname
  useEffect(() => {
    if (lastRedirectRef.current !== location.pathname) {
      redirectCountRef.current = 0;
      lastRedirectRef.current = location.pathname;
    }
  }, [location.pathname]);

  // Jeśli użytkownik nie jest zalogowany i nie jest na stronie logowania
  if (!isAuthenticated && location.pathname !== routes.login) {
    redirectCountRef.current++;

    if (redirectCountRef.current > 3) {
      console.error('[PrivateRoutes] REDIRECT LOOP DETECTED!', {
        timestamp: new Date().toISOString(),
        redirectCount: redirectCountRef.current,
        from: location.pathname,
      });
      // Zatrzymaj pętlę - przekieruj bezpośrednio na login bez dalszych prób
      window.location.href = routes.login;
      return <div>Redirecting to login...</div>;
    }

    console.log(
      '[PrivateRoutes] User not authenticated, redirecting to login',
      {
        timestamp: new Date().toISOString(),
        from: location.pathname,
        redirectCount: redirectCountRef.current,
      },
    );

    return <Navigate to={routes.login} replace state={{ from: location }} />;
  }

  // Jeśli użytkownik nie jest zalogowany ale jest już na stronie logowania,
  // nie rób nic - strona logowania jest obsługiwana poza PrivateRoutes
  if (!isAuthenticated && location.pathname === routes.login) {
    console.log(
      '[PrivateRoutes] User not authenticated but already on login page - should not reach here',
      {
        timestamp: new Date().toISOString(),
        currentPath: location.pathname,
      },
    );
    // To nie powinno się zdarzyć, bo /login jest poza PrivateRoutes
    return <Navigate to={routes.login} replace />;
  }

  const shouldRedirectToHome =
    (location.pathname === routes.vod && isVodRouteForbidden) ||
    (location.pathname === routes.recordings && isNpvrRouteForbidden);

  if (shouldRedirectToHome) {
    console.log('[PrivateRoutes] Route forbidden, redirecting to home', {
      timestamp: new Date().toISOString(),
      from: location.pathname,
    });
    return <Navigate to={routes.home} replace />;
  }

  return <Outlet />;
};
