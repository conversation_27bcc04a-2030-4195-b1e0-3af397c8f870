import { FC, useEffect, useRef } from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';

import { routes } from 'routes/routes-map';
import { useConfig } from 'services/config';
import { useAuthenticationStatus } from 'services/user';

export const PrivateRoutes: FC = () => {
  const { isAuthenticated } = useAuthenticationStatus();
  const location = useLocation();
  const { getTechConfig } = useConfig();
  const techConfig = getTechConfig();

  // Zabezpieczenie przed pętlą przekierowań
  const redirectCountRef = useRef(0);
  const lastPathRef = useRef('');

  const isVodRouteForbidden = !techConfig.mainTabs?.isVodTabVisible;
  const isNpvrRouteForbidden = !techConfig.withVisibleNpvrFeatures;

  // Reset licznika gdy zmieni się ścieżka
  useEffect(() => {
    if (lastPathRef.current !== location.pathname) {
      redirectCountRef.current = 0;
      lastPathRef.current = location.pathname;
    }
  }, [location.pathname]);

  console.log('[PrivateRoutes] Checking access', {
    timestamp: new Date().toISOString(),
    isAuthenticated,
    currentPath: location.pathname,
    isVodRouteForbidden,
    isNpvrRouteForbidden,
    redirectCount: redirectCountRef.current,
  });

  // Jeśli użytkownik nie jest zalogowany, przekieruj na login
  if (!isAuthenticated) {
    redirectCountRef.current++;

    // Zabezpieczenie przed nieskończoną pętlą
    if (redirectCountRef.current > 3) {
      console.error(
        '[PrivateRoutes] REDIRECT LOOP DETECTED! Using window.location.href',
        {
          timestamp: new Date().toISOString(),
          from: location.pathname,
          redirectCount: redirectCountRef.current,
        },
      );

      // Użyj window.location.href jako ostateczności
      window.location.href = routes.login;
      return <div>Redirecting to login...</div>;
    }

    console.log(
      '[PrivateRoutes] User not authenticated, redirecting to login',
      {
        timestamp: new Date().toISOString(),
        from: location.pathname,
        redirectCount: redirectCountRef.current,
      },
    );

    return <Navigate to={routes.login} replace state={{ from: location }} />;
  }

  const shouldRedirectToHome =
    (location.pathname === routes.vod && isVodRouteForbidden) ||
    (location.pathname === routes.recordings && isNpvrRouteForbidden);

  if (shouldRedirectToHome) {
    console.log('[PrivateRoutes] Route forbidden, redirecting to home', {
      timestamp: new Date().toISOString(),
      from: location.pathname,
    });
    return <Navigate to={routes.home} replace />;
  }

  return <Outlet />;
};
