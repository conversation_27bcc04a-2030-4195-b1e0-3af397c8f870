import { FC } from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';

import { routes } from 'routes/routes-map';
import { useConfig } from 'services/config';
import { useAuthenticationStatus } from 'services/user';

export const PrivateRoutes: FC = () => {
  const { isAuthenticated } = useAuthenticationStatus();
  const location = useLocation();
  const { getTechConfig } = useConfig();
  const techConfig = getTechConfig();

  const isVodRouteForbidden = !techConfig.mainTabs?.isVodTabVisible;
  const isNpvrRouteForbidden = !techConfig.withVisibleNpvrFeatures;

  if (!isAuthenticated) {
    return <Navigate to={routes.login} replace state={{ from: location }} />;
  }

  const shouldRedirectToHome =
    (location.pathname === routes.vod && isVodRouteForbidden) ||
    (location.pathname === routes.recordings && isNpvrRouteForbidden);

  if (shouldRedirectToHome) {
    return <Navigate to={routes.home} replace />;
  }

  return <Outlet />;
};
