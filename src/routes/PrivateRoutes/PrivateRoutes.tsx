import { FC } from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';

import { routes } from 'routes/routes-map';
import { useConfig } from 'services/config';
import { useAuthenticationStatus } from 'services/user';

export const PrivateRoutes: FC = () => {
  const { isAuthenticated } = useAuthenticationStatus();
  const location = useLocation();
  const { getTechConfig } = useConfig();
  const techConfig = getTechConfig();

  const isVodRouteForbidden = !techConfig.mainTabs?.isVodTabVisible;
  const isNpvrRouteForbidden = !techConfig.withVisibleNpvrFeatures;

  console.log('[PrivateRoutes] Checking access', {
    timestamp: new Date().toISOString(),
    isAuthenticated,
    currentPath: location.pathname,
    isVodRouteForbidden,
    isNpvrRouteForbidden,
  });

  // <PERSON><PERSON><PERSON> użytkownik nie jest zalogowany, przekieruj na login
  if (!isAuthenticated) {
    console.log(
      '[PrivateRoutes] User not authenticated, redirecting to login',
      {
        timestamp: new Date().toISOString(),
        from: location.pathname,
      },
    );

    return <Navigate to={routes.login} replace state={{ from: location }} />;
  }

  const shouldRedirectToHome =
    (location.pathname === routes.vod && isVodRouteForbidden) ||
    (location.pathname === routes.recordings && isNpvrRouteForbidden);

  if (shouldRedirectToHome) {
    console.log('[PrivateRoutes] Route forbidden, redirecting to home', {
      timestamp: new Date().toISOString(),
      from: location.pathname,
    });
    return <Navigate to={routes.home} replace />;
  }

  return <Outlet />;
};
