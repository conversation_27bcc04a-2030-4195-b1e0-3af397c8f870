import { FC, useEffect, useRef } from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';

import { routes } from 'routes/routes-map';
import { useConfig } from 'services/config';
import { useAuthenticationStatus } from 'services/user';

export const PrivateRoutes: FC = () => {
  const { isAuthenticated } = useAuthenticationStatus();
  const location = useLocation();
  const { getTechConfig } = useConfig();
  const techConfig = getTechConfig();

  const redirectCountRef = useRef(0);
  const lastPathRef = useRef('');

  const isVodRouteForbidden = !techConfig.mainTabs?.isVodTabVisible;
  const isNpvrRouteForbidden = !techConfig.withVisibleNpvrFeatures;

  useEffect(() => {
    if (lastPathRef.current !== location.pathname) {
      redirectCountRef.current = 0;
      lastPathRef.current = location.pathname;
    }
  }, [location.pathname]);

  if (!isAuthenticated) {
    redirectCountRef.current++;

    // if (redirectCountRef.current > 3) {
    //   window.location.href = routes.login;
    //   return <div>Redirecting to login...</div>;
    // }

    return <Navigate to={routes.login} replace state={{ from: location }} />;
  }

  const shouldRedirectToHome =
    (location.pathname === routes.vod && isVodRouteForbidden) ||
    (location.pathname === routes.recordings && isNpvrRouteForbidden);

  if (shouldRedirectToHome) {
    return <Navigate to={routes.home} replace />;
  }

  return <Outlet />;
};
