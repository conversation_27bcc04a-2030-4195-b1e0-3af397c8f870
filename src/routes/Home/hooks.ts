import { useLocalStorage } from 'services/storage';
import { useConfig } from 'services/config';
import { useAuthenticationStatus } from 'services/user';

import { ATV_APP_POPUP_ALREADY_SHOWN_STORAGE } from './constants';

export const useShowAtvAppPromoPopup = () => {
  const { getTechConfig } = useConfig();
  const { isAuthenticated } = useAuthenticationStatus();
  const [isAtvAppPopupAlreadyShown, setIsAtvAppPopupAlreadyShown] =
    useLocalStorage<boolean>(ATV_APP_POPUP_ALREADY_SHOWN_STORAGE, false);

  const canBeVisibleToCurrentUser = Boolean(
    getTechConfig()?.infoScreen?.isVisibleToUser,
  );

  const isPopupOpen =
    isAuthenticated && canBeVisibleToCurrentUser && !isAtvAppPopupAlreadyShown;

  const handlePopupClose = () => {
    setIsAtvAppPopupAlreadyShown(true);
  };

  return { isPopupOpen, handlePopupClose };
};
