import {
  Navigate,
  Route,
  Routes as RouterRoutes,
  useLocation,
} from 'react-router-dom';
import { AnimatePresence } from 'framer-motion';
import { FC } from 'react';

import Channels from 'routes/Channels';
import { Home } from 'routes/Home';
import { Login } from 'routes/Login';
import Program from 'routes/Program';
import { Recordings } from 'routes/Recordings';
import { Settings } from 'routes/Settings';
import VOD from 'routes/VOD';
import { DetailsOverlayWrapper } from 'services/detailsView';
import { useAppLayoutMode } from 'services/appLayoutMode';
import { PlayerMode } from 'features/Player';
import { isInitialPlayback, usePlayerPlayback } from 'features/Player/Context';
import { GlobalLoader } from 'services/loader/GlobalLoader';
import { Header } from 'components/Header';
import { DeviceType, useMobileDetector } from 'hooks';
import { MobileDisabledView } from 'features/MobileDisabledView';

import { routes } from './routes-map';
import * as S from './styles';
import { PrivateRoutes } from './PrivateRoutes';
import { Application } from './Application';

export const Routes: FC = () => {
  const location = useLocation();
  const { mode } = useAppLayoutMode();
  const { playback } = usePlayerPlayback();

  const variants = {
    open: { y: 0 },
    closed: { y: 'calc(100% + 100vh)' },
  };

  const isClosed = mode === PlayerMode.Expanded && !isInitialPlayback(playback);
  const shouldDisplayHeader =
    location.pathname !== routes.login &&
    location.pathname !== routes.application;

  const { isMobile, deviceType } = useMobileDetector();

  const shouldDisplayMobileDisabledView =
    isMobile &&
    deviceType !== DeviceType.web &&
    location.pathname !== routes.application;

  if (shouldDisplayMobileDisabledView) {
    return <MobileDisabledView deviceType={deviceType} />;
  }

  return (
    <DetailsOverlayWrapper>
      <GlobalLoader />
      {shouldDisplayHeader && <Header />}
      <AnimatePresence>
        <S.RoutesWrapper
          animate={isClosed ? 'closed' : 'open'}
          variants={variants}
          transition={{ default: { ease: 'linear' } }}
        >
          <RouterRoutes>
            <Route path={routes.login} element={<Login />} />
            <Route path={routes.application} element={<Application />} />
            <Route element={<PrivateRoutes />}>
              <Route path={routes.home} element={<Home />} />
              <Route path={routes.channels} element={<Channels />} />
              <Route path={routes.program} element={<Program />} />
              <Route path={routes.vod} element={<VOD />} />
              <Route path={routes.recordings} element={<Recordings />} />
              <Route path={routes.settings} element={<Settings />} />
              {/* Catch-all dla nieznanych tras w obrębie PrivateRoutes */}
              <Route path='*' element={<Navigate to={routes.home} replace />} />
            </Route>
          </RouterRoutes>
        </S.RoutesWrapper>
      </AnimatePresence>
    </DetailsOverlayWrapper>
  );
};
