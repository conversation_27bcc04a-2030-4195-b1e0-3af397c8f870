import {
  Context,
  createContext,
  FC,
  PropsWithChildren,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';

import { useAuthenticationStatus } from 'services/user';
import { useLocalStorage } from 'services/storage/hook';
import { useConfig } from 'services/config';
import { useRegionalTvListQuery } from 'services/api/newApi/live/channels';

import { RegionalTvContextValue, RegionalTvElementType } from './types';
import {
  EMPTY_REGION_VALUE,
  REGIONAL_LOCAL_STORAGE_KEY,
  REGIONAL_NOT_SELECTED_ELEMENT,
} from './constants';
import { RegionalTvSelectBoard } from './RegionalTvSelectBoard';

const RegionalTvContext: Context<RegionalTvContextValue> =
  createContext<RegionalTvContextValue>({} as RegionalTvContextValue);

export const RegionalTvProvider: FC<PropsWithChildren<unknown>> = ({
  children,
}) => {
  const { isAuthenticated } = useAuthenticationStatus();
  const { data: regionalTvListData } = useRegionalTvListQuery(isAuthenticated);
  const { config } = useConfig();
  const { appConfig } = config;

  const [userRegionalTv, setUserRegionalTv] =
    useLocalStorage<RegionalTvElementType>(
      REGIONAL_LOCAL_STORAGE_KEY,
      EMPTY_REGION_VALUE,
    );
  const [isRegionalSelectViewVisible, setIsRegionalSelectViewVisible] =
    useState(false);
  const [afterAcceptCallback, setAfterAcceptCallback] = useState(
    () => () => {},
  );

  const defaultUserRegionalTvValue = useMemo(() => {
    if (userRegionalTv?.label) {
      return userRegionalTv;
    }

    return REGIONAL_NOT_SELECTED_ELEMENT;
  }, [userRegionalTv]);

  const isOnlyOneRegionalChannel =
    regionalTvListData?.regionalTvPackageList.length === 1;

  const regionalTvArray = isOnlyOneRegionalChannel
    ? (regionalTvListData?.regionalTvPackageList[0].regionalTvList ?? []).map(
        (regionalChannel) => {
          return {
            label: regionalChannel.channelName,
            value: appConfig?.techConfig.useOptionalRegionParam
              ? regionalChannel.extCdnUrlSuffix
              : regionalChannel.liveUrlSuffix,
          };
        },
      )
    : [];

  const isRegionNotSelectedBefore = useMemo(() => {
    return !userRegionalTv?.label;
  }, [userRegionalTv?.label]);

  const showRegionalTvSelect = useCallback(
    (handleAccept: () => void) => {
      setAfterAcceptCallback(() => handleAccept);
      setIsRegionalSelectViewVisible(true);
    },
    [setAfterAcceptCallback, setIsRegionalSelectViewVisible],
  );

  const clearRegionalTvValue = useCallback(() => {
    setUserRegionalTv(EMPTY_REGION_VALUE);
  }, [setUserRegionalTv]);

  useEffect(() => {
    if (!isAuthenticated) {
      return clearRegionalTvValue();
    }
  }, [clearRegionalTvValue, isAuthenticated]);

  const value = {
    regionalTvArray,
    userRegionalTv,
    setUserRegionalTv,
    defaultUserRegionalTvValue,
    showRegionalTvSelect,
    isRegionNotSelectedBefore,
    setIsRegionalSelectViewVisible,
    isRegionalSelectViewVisible,
  };

  return (
    <RegionalTvContext.Provider value={value}>
      {isRegionalSelectViewVisible && (
        <RegionalTvSelectBoard
          setIsRegionalSelectViewVisible={setIsRegionalSelectViewVisible}
          afterAcceptCallback={afterAcceptCallback}
          setUserRegion={setUserRegionalTv}
          regionalTvArray={regionalTvArray}
          defaultUserRegionalTvValue={defaultUserRegionalTvValue}
        />
      )}

      {children}
    </RegionalTvContext.Provider>
  );
};

export const useRegionalTvContext = (): RegionalTvContextValue => {
  const context = useContext(RegionalTvContext);

  if (context) {
    return context;
  }
  throw new Error('Component beyond RegionalTvContext');
};
