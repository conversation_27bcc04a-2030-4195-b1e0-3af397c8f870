import { useCallback, useEffect, useMemo } from 'react';
import getResult from 'ua-parser-js';

import { useGetPmsTokenMutation } from 'services/api/newApi/core/device';
import {
  HouseholdResponse,
  useGetHouseholdMutation,
} from 'services/api/newApi/core/household';
import { useConfig } from 'services/config';
import {
  BasePmsReport,
  EventType,
  PeriodicPmsReport,
  postDiagnosticPeriodicReport,
} from 'services/api/pmsApi';
import { generateTerminalSerialNumber, getBrowserType } from 'services/device';
import { useLogger } from 'services/logger';
import { isTimestampNotFromToday } from 'utils/dateUtils';

import { TIMESTAMP_OF_LAST_PMS_REPORT } from './constants';
import { getCookieValue } from './utils';

import packageJson from '../../../package.json';

export const useCreatePmsReport = () => {
  const device = useMemo(() => getResult(), []);

  const {
    config: {
      terminal: { deviceManufacture, serialNumberType },
      pms: { accessType, platform },
    },
  } = useConfig();

  const browserType = getBrowserType();

  const createBasePmsReport = useCallback(
    (secret: string, housholdInfo: HouseholdResponse): BasePmsReport => {
      const deviceSerialNumber = generateTerminalSerialNumber(
        browserType,
        serialNumberType,
        housholdInfo.householdExtId,
      );

      return {
        secret: secret,
        videoId: housholdInfo.householdExtId,
        deviceSerialNumber: deviceSerialNumber,
        accessType: accessType,
        platform: platform,
        messageTimestamp: new Date().toISOString(),
        orangeApplicationVersion: packageJson.version,
        deviceModel: getBrowserType(),
      };
    },
    [accessType, browserType, platform, serialNumberType],
  );

  const createPeriodicPmsReport = (
    secret: string,
    housholdInfo: HouseholdResponse,
  ): PeriodicPmsReport => {
    const screenResolution = `${window.screen.width}x${window.screen.height}`;
    return {
      ...createBasePmsReport(secret, housholdInfo),
      eventType: EventType.PERIODIC,
      deviceManufacturer: deviceManufacture,
      ...(device.os.name && { osVersion: device.os.name }),
      ...(device.browser.version && {
        browserVersion: device.browser.version,
      }),
      hhTech: housholdInfo.hhTech,
      offer: housholdInfo.offer,
      screenResolution: screenResolution,
    };
  };

  return {
    createBasePmsReport,
    createPeriodicPmsReport,
  };
};

export const usePmsReporterHook = () => {
  const { logger } = useLogger();
  const { mutateAsync: getPmsToken } = useGetPmsTokenMutation();
  const { mutateAsync: getHouseholdInfo } = useGetHouseholdMutation();
  const { createPeriodicPmsReport } = useCreatePmsReport();

  const clearTimestampOfLastPmsReport = useCallback(() => {
    localStorage.setItem(TIMESTAMP_OF_LAST_PMS_REPORT, JSON.stringify(null));
  }, []);

  const sendPeriodicPmsReport = useCallback(async () => {
    const currentTimestamp = new Date().getTime();
    const timestampOfLastPmsReport = Number(
      localStorage.getItem(TIMESTAMP_OF_LAST_PMS_REPORT),
    );
    const canSendNewPmsReport =
      !timestampOfLastPmsReport ||
      isTimestampNotFromToday(timestampOfLastPmsReport);

    const isAuthenticated = Boolean(getCookieValue('Authenticated'));
    if (isAuthenticated && canSendNewPmsReport) {
      localStorage.setItem(
        TIMESTAMP_OF_LAST_PMS_REPORT,
        JSON.stringify(currentTimestamp),
      );
      try {
        const householdInfo = await getHouseholdInfo();
        const pmsToken = await getPmsToken();
        const report = createPeriodicPmsReport(pmsToken, householdInfo);
        postDiagnosticPeriodicReport(report);
      } catch (error: unknown) {
        logger.error(`Error while sending PMS report ${error}`);
      }
    }
  }, [createPeriodicPmsReport, getPmsToken, getHouseholdInfo, logger]);

  const handlePageVisibilityChange = useCallback(() => {
    if (document.visibilityState === 'visible') {
      sendPeriodicPmsReport();
    }
  }, [sendPeriodicPmsReport]);

  useEffect(() => {
    sendPeriodicPmsReport();
    document.addEventListener('visibilitychange', handlePageVisibilityChange);
    return () => {
      document.removeEventListener(
        'visibilitychange',
        handlePageVisibilityChange,
      );
    };
    // send request only on page initial load or visibility change
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return { clearTimestampOfLastPmsReport };
};
