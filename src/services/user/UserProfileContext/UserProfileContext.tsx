import {
  createContext,
  FC,
  PropsWithChildren,
  useContext,
  useEffect,
  useMemo,
} from 'react';

import { useCoreHouseholdQuery } from 'services/api/newApi/core/household';
import { globalConfig } from 'services/config/config';
import { UserTheme, useUserTheme } from 'theme/UserThemeContext';

import { UserProfileValue } from './types';

import { useAuthenticationStatus } from '../AuthenticationStatusContext/AuthenticationStatusContext';

export const UserProfileContext = createContext<UserProfileValue>(
  {} as UserProfileValue,
);

export const UserProfileProvider: FC<PropsWithChildren<unknown>> = ({
  children,
}) => {
  const { dth } = globalConfig.unsupportedTech;
  const { njutv } = globalConfig.hiddenNavigationItems;
  const mobileTechnologies = globalConfig.mobileTechnologies;

  const { setUserTheme } = useUserTheme();
  const { isAuthenticated } = useAuthenticationStatus();
  const { data: householdInfo } = useCoreHouseholdQuery(isAuthenticated);

  const {
    hhTech,
    npvr: isNpvrService,
    parentalPin: isParentalPinDefined,
  } = householdInfo || {};

  const isMobileTvUser = useMemo(() => {
    if (hhTech) {
      return mobileTechnologies.includes(hhTech);
    }
    return false;
  }, [hhTech, mobileTechnologies]);

  const isDthUser = useMemo(() => {
    return hhTech === dth;
  }, [dth, hhTech]);

  const isNjutvUser = useMemo(() => {
    return hhTech === njutv;
  }, [njutv, hhTech]);

  useEffect(() => {
    if (isNjutvUser) {
      return setUserTheme(UserTheme.NJU);
    }
    setUserTheme(UserTheme.DEFAULT);
  }, [hhTech, isNjutvUser, setUserTheme]);

  return (
    <UserProfileContext.Provider
      value={{
        isMobileTvUser,
        isDthUser,
        isNjutvUser,
        userHhTech: hhTech || '',
        isParentalPinDefined,
        isNpvrService,
      }}
    >
      {children}
    </UserProfileContext.Provider>
  );
};

export const useUserProfile = (): UserProfileValue => {
  const context = useContext(UserProfileContext);

  if (context) {
    return context;
  }
  throw new Error('Component beyond UserProfileContext');
};
