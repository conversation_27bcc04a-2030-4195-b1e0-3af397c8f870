import {
  createContext,
  FC,
  PropsWithChildren,
  useCallback,
  useContext,
} from 'react';
import { useNavigate } from 'react-router-dom';

import {
  useAutoLoginMutation,
  useLoginMutation,
  useLogoutMutation,
} from 'services/api/newApi/auth';
import { globalConfig } from 'services/config/config';
import { getUserErrorType, useErrorScreen } from 'services/error';
import { usePmsReporter } from 'services/pmsReporter';
import { routes } from 'routes/routes-map';

import { UserSessionValue } from './types';

import { useAuthenticationStatus } from '../AuthenticationStatusContext/AuthenticationStatusContext';
import {
  deleteRefreshToken,
  setNewRefreshToken,
} from '../RefreshTokenHandlers';

export const UserSessionContext = createContext<UserSessionValue>(
  {} as UserSessionValue,
);

export const UserSessionProvider: FC<PropsWithChildren<unknown>> = ({
  children,
}) => {
  const { showErrorModal } = useErrorScreen();
  const { setAuthenticated } = useAuthenticationStatus();
  const { mutateAsync: loginAsync } = useLoginMutation();
  const { isLoading: isFullLoginLoading } = useLoginMutation();
  const { isSuccess: isFullLoginSuccess } = useLoginMutation();
  const { mutateAsync: logoutAsync } = useLogoutMutation();
  const { mutateAsync: autoLoginAsync } = useAutoLoginMutation();
  const { clearTimestampOfLastPmsReport } = usePmsReporter();
  const navigate = useNavigate();

  console.log('[UserSessionProvider] Component render', {
    timestamp: new Date().toISOString(),
    currentPath: window.location.pathname,
  });

  const quickLogin = useCallback(
    async (code: string) => {
      console.log('[UserSession] quickLogin started', {
        timestamp: new Date().toISOString(),
      });

      const quickLoginResponse = await autoLoginAsync({
        autologinPin: code,
        deviceAppVersion: '',
        deviceFirmwareVersion: '',
        deviceInfo: '',
        deviceMac: '',
        deviceModel: '',
        deviceSn: '',
        deviceType: globalConfig.api.deviceType,
      });

      console.log('[UserSession] quickLogin success, setting authenticated', {
        timestamp: new Date().toISOString(),
      });

      setAuthenticated(true);
      setNewRefreshToken(quickLoginResponse.data.refreshToken);
    },
    [autoLoginAsync, setAuthenticated],
  );

  const fullLogin = useCallback(
    async (username: string, code: string) => {
      console.log('[UserSession] fullLogin started', {
        timestamp: new Date().toISOString(),
        username,
      });

      const fullLoginResponse = await loginAsync({
        login: username,
        password: code,
      });

      console.log('[UserSession] fullLogin success, setting authenticated', {
        timestamp: new Date().toISOString(),
      });

      setAuthenticated(true);
      setNewRefreshToken(fullLoginResponse.data.refreshToken);
    },
    [loginAsync, setAuthenticated],
  );

  const logout = useCallback(async (): Promise<void> => {
    console.log('[UserSession] ========== LOGOUT STARTED ==========', {
      timestamp: new Date().toISOString(),
      currentPath: window.location.pathname,
      caller: new Error().stack?.split('\n')[2]?.trim(),
    });

    console.log('[UserSession] Step 1: Deleting refresh token', {
      timestamp: new Date().toISOString(),
    });
    deleteRefreshToken();

    console.log('[UserSession] Step 2: Calling logout API', {
      timestamp: new Date().toISOString(),
    });

    try {
      await logoutAsync();
      console.log('[UserSession] Logout API success', {
        timestamp: new Date().toISOString(),
      });
    } catch (error: unknown) {
      console.error('[UserSession] Logout API error', {
        timestamp: new Date().toISOString(),
        error,
      });
      showErrorModal(getUserErrorType(error));
    }

    console.log('[UserSession] Step 3: Clearing PMS report timestamp', {
      timestamp: new Date().toISOString(),
    });
    clearTimestampOfLastPmsReport();

    console.log(
      '[UserSession] Step 4: Navigating to login BEFORE setting authenticated to false',
      {
        timestamp: new Date().toISOString(),
        targetRoute: routes.login,
      },
    );

    // Najpierw nawiguj, potem ustaw authenticated na false
    // To zapobiega konfliktowi z PrivateRoutes
    navigate(routes.login, { replace: true });

    console.log('[UserSession] Step 5: Setting authenticated to false', {
      timestamp: new Date().toISOString(),
    });
    setAuthenticated(false);

    console.log('[UserSession] ========== LOGOUT COMPLETED ==========', {
      timestamp: new Date().toISOString(),
    });
  }, [
    clearTimestampOfLastPmsReport,
    logoutAsync,
    setAuthenticated,
    navigate,
    showErrorModal,
  ]);

  return (
    <UserSessionContext.Provider
      value={{
        quickLogin,
        fullLogin,
        isFullLoginLoading,
        isFullLoginSuccess,
        logout,
      }}
    >
      {children}
    </UserSessionContext.Provider>
  );
};

export const useUserSession = (): UserSessionValue => {
  const context = useContext(UserSessionContext);

  if (context) {
    return context;
  }
  throw new Error('Component beyond UserSessionContext');
};
