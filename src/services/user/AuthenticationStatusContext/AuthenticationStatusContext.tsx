import {
  createContext,
  FC,
  PropsWithChildren,
  useCallback,
  useContext,
  useState,
} from 'react';
import { useCookies } from 'react-cookie';

import { getExpirationDate } from 'utils/dateUtils';

import { AuthenticationStatusValue } from './types';

export const AuthenticationStatusContext =
  createContext<AuthenticationStatusValue>({} as AuthenticationStatusValue);

export const AuthenticationStatusProvider: FC<PropsWithChildren<unknown>> = ({
  children,
}) => {
  const [cookies, setCookie, removeCookie] = useCookies(['Authenticated']);

  const [isAuthenticated, setAuthenticatedFlag] = useState<boolean>(
    Boolean(cookies.Authenticated),
  );

  const setAuthenticated = useCallback(
    (value: boolean) => {
      if (value) {
        setCookie('Authenticated', value, {
          expires: getExpirationDate(30),
        });
      } else {
        removeCookie('Authenticated');
      }

      setAuthenticatedFlag(value);
    },
    [setCookie, removeCookie],
  );

  return (
    <AuthenticationStatusContext.Provider
      value={{ isAuthenticated, setAuthenticated }}
    >
      {children}
    </AuthenticationStatusContext.Provider>
  );
};

export const useAuthenticationStatus = (): AuthenticationStatusValue => {
  const context = useContext(AuthenticationStatusContext);

  if (context) {
    return context;
  }
  throw new Error('Component beyond AuthenticationStatusContext');
};
