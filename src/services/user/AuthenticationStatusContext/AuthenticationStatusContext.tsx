import {
  createContext,
  FC,
  PropsWithChildren,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react';
import { useCookies } from 'react-cookie';
import { useLocation, useNavigate } from 'react-router-dom';

import { getExpirationDate } from 'utils/dateUtils';
import { routes } from 'routes/routes-map';
import { deleteRefreshToken } from 'services/user/RefreshTokenHandlers';

import { AuthenticationStatusValue } from './types';

export const AuthenticationStatusContext =
  createContext<AuthenticationStatusValue>({} as AuthenticationStatusValue);

export const AuthenticationStatusProvider: FC<PropsWithChildren<unknown>> = ({
  children,
}) => {
  const [cookies, setCookie, removeCookie] = useCookies(['Authenticated']);
  const navigate = useNavigate();
  const location = useLocation();

  const [isAuthenticated, setAuthenticatedFlag] = useState<boolean>(
    Boolean(cookies.Authenticated),
  );

  useEffect(() => {
    setAuthenticatedFlag(Boolean(cookies.Authenticated));
  }, [cookies.Authenticated]);

  useEffect(() => {
    if (!isAuthenticated) {
      const isLoginPage = location.pathname.includes('/login');

      if (!isLoginPage) {
        deleteRefreshToken();
        navigate(routes.login, { replace: true });
      }
    }
  }, [isAuthenticated, navigate, location.pathname]);

  const setAuthenticated = useCallback(
    (value: boolean) => {
      if (value) {
        setCookie('Authenticated', value, {
          expires: getExpirationDate(30),
        });
      } else {
        removeCookie('Authenticated');
      }

      setAuthenticatedFlag(value);
    },
    [setCookie, removeCookie],
  );

  return (
    <AuthenticationStatusContext.Provider
      value={{ isAuthenticated, setAuthenticated }}
    >
      {children}
    </AuthenticationStatusContext.Provider>
  );
};

export const useAuthenticationStatus = (): AuthenticationStatusValue => {
  const context = useContext(AuthenticationStatusContext);

  if (context) {
    return context;
  }
  throw new Error('Component beyond AuthenticationStatusContext');
};
