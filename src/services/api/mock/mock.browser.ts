import { rest, setupWorker } from 'msw';
declare global {
  interface Window {
    msw: any;
  }
}

const mockChannelsResponse = rest.get(
  'gpapi2/live/channel/all?deviceCat=otg',
  (req, res, ctx) => {
    return res(ctx.status(401));
  },
);

// Mock refresh token endpoint - też zwraca 401 żeby symulować nieważny refresh token
const mockRefreshResponse = rest.post(
  'gpapi2/auth/refresh',
  (req, res, ctx) => {
    return res(ctx.status(401));
  },
);

export const worker = setupWorker(mockChannelsResponse, mockRefreshResponse);

window.msw = {
  worker,
  rest,
};
