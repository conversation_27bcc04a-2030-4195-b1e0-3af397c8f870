import { rest, setupWorker } from 'msw';
declare global {
  interface Window {
    msw: any;
  }
}

// Mock dla edge case: 401 → Refresh 200 → Retry 401
// Używamy licznika żeby śledzić wywołania
let programDetailsCallCount = 0;

const mockProgramDetailsResponse = rest.get(
  '/gpapi2/core/tvguide/details',
  (_req, res, ctx) => {
    programDetailsCallCount++;

    console.log(`[MSW] Program details call #${programDetailsCallCount} → 401`);

    // ZAWSZE zwracamy 401 (pierwsze zapytanie i retry po refresh)
    return res(
      ctx.status(401),
      ctx.json({
        programExtId: 'lid700888192036764',
        mediaId: '526063-1008704',
        seriesId: '526063',
        name: '<PERSON>',
        channelExtId: '14217',
        channelName: 'Cartoon Network HD',
        startTimeUtc: 1752072300,
        endTimeUtc: 1752072900,
        image: 'akpah2237572.jpg',
        description: '<PERSON> i jego kumple, <PERSON><PERSON>, <PERSON> i <PERSON>son...',
        genre: 'serial animowany',
        year: '2013-2015',
        countries: ['USA'],
        series: {
          episodeTitle: 'Pacjent specjalnej troski',
          episodeNumber: '26',
          seasonNumber: '1',
        },
        imagePaths: {
          minih: '/mnapi/gopher-2epgminih/',
          miniv: '/mnapi/gopher-2epgminiv/',
          small: '/mnapi/gopher-2epgsmall/',
          standard: '/mnapi/gopher-2epgstd/',
          thumb: '/mnapi/gopher-2epgthumb/',
        },
      }),
    );
  },
);
          {
            channelExtId: '14999',
            name: 'TVP 2 HD',
            channelNumber: 2,
            category: 'Ogólne',
            logoSignature:
              'L1ZPRC80NDdDMkQ3RkZBRkEyQkQwNTc3OTUwMjk1RURBRkEyMzY3ODFCNEIzLnBuZw==',
            logoImageId: '4f55ee9933d3036f16e6d2795d72911d',
            catchupDuration: 604800,
            isPayPerView: false,
            isRegionalTv: false,
            isHomeZoneRestricted: false,
            isRecordingAllowed: false,
            isInteractive: false,
            isSubscribed: true,
            frameUrl:
              'https://img.cache.orange.pl/otv/OTF/0/0/0/0/0/pool04/bpk-tv/14999/DASH/dash/thumbnails/TVP2HD-thumbnail-[time].jpeg',
            playFeatures: {
              otg: {
                isChannelAllowed: true,
                isCatchUp: false,
                isStartOver: false,
                isNpvr: false,
              },
              conTv: {
                isChannelAllowed: true,
                isCatchUp: true,
                isStartOver: true,
                isNpvr: false,
              },
              stb: {
                isChannelAllowed: true,
                isCatchUp: true,
                isStartOver: true,
                isNpvr: false,
              },
              boxless: {
                isChannelAllowed: true,
                isCatchUp: true,
                isStartOver: true,
                isNpvr: false,
              },
            },
          },
          {
            channelExtId: '14171',
            name: 'TVN HD',
            channelNumber: 3,
            category: 'Ogólne',
            logoSignature: 'L2F0dGFjaG1lbnRzL3R2bl8yMDI0XzE1OXgxMTAucG5n',
            logoImageId: '8b1ce7658114189435db07b27e504b48',
            catchupDuration: 172800,
            isPayPerView: false,
            isRegionalTv: false,
            isHomeZoneRestricted: false,
            isRecordingAllowed: true,
            isInteractive: false,
            isSubscribed: true,
            dai: 'DAIWBD',
            frameUrl:
              'https://img.cache.orange.pl/otv/OTF/0/0/0/0/0/pool02/bpk-tv/14171/DASH/dash/thumbnails/TVNHD-thumbnail-[time].jpeg',
            playFeatures: {
              otg: {
                isChannelAllowed: true,
                isCatchUp: false,
                isStartOver: false,
                isNpvr: false,
              },
              conTv: {
                isChannelAllowed: true,
                isCatchUp: true,
                isStartOver: true,
                isNpvr: true,
                isFastForwardBlocked: true,
              },
              stb: {
                isChannelAllowed: true,
                isCatchUp: true,
                isStartOver: true,
                isNpvr: true,
                isFastForwardBlocked: true,
              },
              boxless: {
                isChannelAllowed: true,
                isCatchUp: true,
                isStartOver: true,
                isNpvr: true,
                isFastForwardBlocked: true,
              },
            },
          },
        ],
      }),
    );
  },
);

export const worker = setupWorker(mockResponse);

window.msw = {
  worker,
  rest,
};
