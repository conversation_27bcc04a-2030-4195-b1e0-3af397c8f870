import { rest, setupWorker } from 'msw';
declare global {
  interface Window {
    msw: any;
  }
}

// Mock dla kanałów - zwraca normalne dane
const mockChannelsResponse = rest.get(
  '/gpapi2/live/channel/all',
  (_req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        channelListLength: 183,
        channelList: [
          {
            channelExtId: '14135',
            name: 'TVP 1 HD',
            channelNumber: 1,
            category: 'Ogólne',
            logoSignature:
              'L1ZPRC9EMkE1NUMwNUJDMEY4NTAwMDUyMDFDOEI4ODNCQUE1RTg0MjI4OEEyLnBuZw==',
            logoImageId: 'cf76c8f2612158e1920e728765223e1f',
            catchupDuration: 604800,
            isPayPerView: false,
            isRegionalTv: false,
            isHomeZoneRestricted: false,
            isRecordingAllowed: false,
            isInteractive: false,
            isSubscribed: true,
            frameUrl:
              'https://img.cache.orange.pl/otv/OTF/0/0/0/0/0/pool02/bpk-tv/14135/DASH/dash/thumbnails/TVP1HD-thumbnail-[time].jpeg',
            playFeatures: {
              otg: {
                isChannelAllowed: true,
                isCatchUp: false,
                isStartOver: false,
                isNpvr: false,
              },
              conTv: {
                isChannelAllowed: true,
                isCatchUp: true,
                isStartOver: true,
                isNpvr: false,
              },
              stb: {
                isChannelAllowed: true,
                isCatchUp: true,
                isStartOver: true,
                isNpvr: false,
              },
              boxless: {
                isChannelAllowed: true,
                isCatchUp: true,
                isStartOver: true,
                isNpvr: false,
              },
            },
          },
          {
            channelExtId: '14999',
            name: 'TVP 2 HD',
            channelNumber: 2,
            category: 'Ogólne',
            logoSignature:
              'L1ZPRC80NDdDMkQ3RkZBRkEyQkQwNTc3OTUwMjk1RURBRkEyMzY3ODFCNEIzLnBuZw==',
            logoImageId: '4f55ee9933d3036f16e6d2795d72911d',
            catchupDuration: 604800,
            isPayPerView: false,
            isRegionalTv: false,
            isHomeZoneRestricted: false,
            isRecordingAllowed: false,
            isInteractive: false,
            isSubscribed: true,
            frameUrl:
              'https://img.cache.orange.pl/otv/OTF/0/0/0/0/0/pool04/bpk-tv/14999/DASH/dash/thumbnails/TVP2HD-thumbnail-[time].jpeg',
            playFeatures: {
              otg: {
                isChannelAllowed: true,
                isCatchUp: false,
                isStartOver: false,
                isNpvr: false,
              },
              conTv: {
                isChannelAllowed: true,
                isCatchUp: true,
                isStartOver: true,
                isNpvr: false,
              },
              stb: {
                isChannelAllowed: true,
                isCatchUp: true,
                isStartOver: true,
                isNpvr: false,
              },
              boxless: {
                isChannelAllowed: true,
                isCatchUp: true,
                isStartOver: true,
                isNpvr: false,
              },
            },
          },
          {
            channelExtId: '14171',
            name: 'TVN HD',
            channelNumber: 3,
            category: 'Ogólne',
            logoSignature: 'L2F0dGFjaG1lbnRzL3R2bl8yMDI0XzE1OXgxMTAucG5n',
            logoImageId: '8b1ce7658114189435db07b27e504b48',
            catchupDuration: 172800,
            isPayPerView: false,
            isRegionalTv: false,
            isHomeZoneRestricted: false,
            isRecordingAllowed: true,
            isInteractive: false,
            isSubscribed: true,
            dai: 'DAIWBD',
            frameUrl:
              'https://img.cache.orange.pl/otv/OTF/0/0/0/0/0/pool02/bpk-tv/14171/DASH/dash/thumbnails/TVNHD-thumbnail-[time].jpeg',
            playFeatures: {
              otg: {
                isChannelAllowed: true,
                isCatchUp: false,
                isStartOver: false,
                isNpvr: false,
              },
              conTv: {
                isChannelAllowed: true,
                isCatchUp: true,
                isStartOver: true,
                isNpvr: true,
                isFastForwardBlocked: true,
              },
              stb: {
                isChannelAllowed: true,
                isCatchUp: true,
                isStartOver: true,
                isNpvr: true,
                isFastForwardBlocked: true,
              },
              boxless: {
                isChannelAllowed: true,
                isCatchUp: true,
                isStartOver: true,
                isNpvr: true,
                isFastForwardBlocked: true,
              },
            },
          },
        ],
      }),
    );
  },
);

// Mock dla szczegółów programu - zwraca 401 żeby przetestować wylogowanie
// Normalnie zwróciłby takie dane:
// {
//   "programExtId": "lid700888192036764",
//   "mediaId": "526063-1008704",
//   "seriesId": "526063",
//   "name": "Clarence",
//   "channelExtId": "14217",
//   "channelName": "Cartoon Network HD",
//   "startTimeUtc": 1752072300,
//   "endTimeUtc": 1752072900,
//   "image": "akpah2237572.jpg",
//   "description": "Clarence i jego kumple, Sumo, Jeff i Belson...",
//   "genre": "serial animowany",
//   "year": "2013-2015",
//   "countries": ["USA"],
//   "series": {
//     "episodeTitle": "Pacjent specjalnej troski",
//     "episodeNumber": "26",
//     "seasonNumber": "1"
//   },
//   "imagePaths": {
//     "minih": "/mnapi/gopher-2epgminih/",
//     "miniv": "/mnapi/gopher-2epgminiv/",
//     "small": "/mnapi/gopher-2epgsmall/",
//     "standard": "/mnapi/gopher-2epgstd/",
//     "thumb": "/mnapi/gopher-2epgthumb/"
//   }
// }
// ALE dla testów zwracamy 401:
const mockProgramDetailsResponse = rest.get(
  '/gpapi2/core/tvguide/details',
  (_req, res, ctx) => {
    console.log(
      '[MSW] Mocking program details request with 401 (normally would return Clarence data)',
    );
    return res(ctx.status(401));
  },
);

// Mock refresh token endpoint - zwraca 401 żeby symulować nieważny refresh token
const mockRefreshResponse = rest.post(
  '/gpapi2/auth/refresh',
  (_req, res, ctx) => {
    console.log('[MSW] Mocking refresh request with 401');
    return res(ctx.status(401));
  },
);

export const worker = setupWorker(
  mockChannelsResponse, // Kanały działają normalnie
  mockProgramDetailsResponse, // Szczegóły programu zwracają 401
  mockRefreshResponse, // Refresh token też zwraca 401
);

window.msw = {
  worker,
  rest,
};
