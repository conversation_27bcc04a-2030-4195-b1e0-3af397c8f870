import { rest, setupWorker } from 'msw';
declare global {
  interface Window {
    msw: any;
  }
}

// Mock dla edge case: 401 → Refresh 200 → Retry 401
// Używamy licznika żeby śledzić wywołania
let programDetailsCallCount = 0;

const mockProgramDetailsResponse = rest.get(
  '/gpapi2/core/tvguide/details',
  (req, res, ctx) => {
    // Sprawdź czy to zapytanie o konkretny program
    const programExtId = req.url.searchParams.get('programExtId');
    console.log(
      `[MSW] Program details for ${programExtId}, call #${
        programDetailsCallCount + 1
      } → 401`,
    );
    programDetailsCallCount++;

    console.log(`[MSW] Program details call #${programDetailsCallCount} → 401`);

    // ZAWSZE zwracamy 401 (pierwsze zapytanie i retry po refresh)
    return res(
      ctx.status(401),
      ctx.json({
        programExtId: 'lid700888192036764',
        mediaId: '526063-1008704',
        seriesId: '526063',
        name: '<PERSON>',
        channelExtId: '14217',
        channelName: 'Cartoon Network HD',
        startTimeUtc: 1752072300,
        endTimeUtc: 1752072900,
        image: 'akpah2237572.jpg',
        description: 'Clarence i jego kumple, Sumo, Jeff i Belson...',
        genre: 'serial animowany',
        year: '2013-2015',
        countries: ['USA'],
        series: {
          episodeTitle: 'Pacjent specjalnej troski',
          episodeNumber: '26',
          seasonNumber: '1',
        },
        imagePaths: {
          minih: '/mnapi/gopher-2epgminih/',
          miniv: '/mnapi/gopher-2epgminiv/',
          small: '/mnapi/gopher-2epgsmall/',
          standard: '/mnapi/gopher-2epgstd/',
          thumb: '/mnapi/gopher-2epgthumb/',
        },
      }),
    );
  },
);

// Mock refresh endpoint - zwraca 200 (udany refresh!)
const mockRefreshResponse = rest.post(
  '/gpapi2/auth/refresh',
  (_req, res, ctx) => {
    console.log('[MSW] Refresh token request → 200 (SUCCESS)');
    return res(
      ctx.status(200),
      ctx.json({
        refreshToken: 'new-fake-refresh-token-12345',
      }),
    );
  },
);

export const worker = setupWorker(
  mockProgramDetailsResponse, // Program details → ZAWSZE 401
  mockRefreshResponse, // Refresh → 200 (udany!)
);

window.msw = {
  worker,
  rest,
};
