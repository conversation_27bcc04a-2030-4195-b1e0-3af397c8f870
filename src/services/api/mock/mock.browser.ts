import { rest, setupWorker } from 'msw';
declare global {
  interface Window {
    msw: any;
  }
}

// Mock dla kanałów - zwraca normalne dane
const mockChannelsResponse = rest.get(
  '/gpapi2/core/tvguide/details?programExtId=lid700888192036764&deviceCat=otg',
  (_req, res, ctx) => {
    return res(
      ctx.status(401),
      ctx.json({
        programExtId: 'lid700888192036764',
        mediaId: '526063-1008704',
        seriesId: '526063',
        name: '<PERSON>',
        channelExtId: '14217',
        channelName: 'Cartoon Network HD',
        startTimeUtc: 1752072300,
        endTimeUtc: 1752072900,
        image: 'akpah2237572.jpg',
        description: '<PERSON> i jego kumple, Sumo, <PERSON> i Belson...',
        genre: 'serial animowany',
        year: '2013-2015',
        countries: ['USA'],
        series: {
          episodeTitle: '<PERSON>jent specjalnej troski',
          episodeNumber: '26',
          seasonNumber: '1',
        },
        imagePaths: {
          minih: '/mnapi/gopher-2epgminih/',
          miniv: '/mnapi/gopher-2epgminiv/',
          small: '/mnapi/gopher-2epgsmall/',
          standard: '/mnapi/gopher-2epgstd/',
          thumb: '/mnapi/gopher-2epgthumb/',
        },
      }),
    );
  },
);

export const worker = setupWorker(mockChannelsResponse);

window.msw = {
  worker,
  rest,
};
