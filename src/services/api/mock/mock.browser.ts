import { rest, setupWorker } from 'msw';
declare global {
  interface Window {
    msw: any;
    testCase: string; // Przełącznik testów
  }
}

// Przełącznik testów - zmień w konsoli: window.testCase = 'case1'
const currentTestCase = 'case1'; // case1, case2, case3, case4

// Liczniki dla śledzenia wywołań
let programDetailsCallCount = 0;
const refreshCallCount = 0;

// Mock dla szczegółów programu
const mockProgramDetailsResponse = rest.get(
  '/gpapi2/core/tvguide/details',
  (req, res, ctx) => {
    programDetailsCallCount++;
    const programExtId = req.url.searchParams.get('programExtId');

    console.log(
      `[MSW] Program details for ${programExtId}, call #${programDetailsCallCount}`,
    );
    console.log(`[MSW] Current test case: ${currentTestCase}`);

    if (currentTestCase === 'case1') {
      // Case 1: 401 → Refresh 401 → Wylogowanie
      console.log(`[MSW] Case 1: Always 401`);
      return res(ctx.status(401));
    }

    if (currentTestCase === 'case2') {
      // Case 2: 401 → Refresh 200 → Retry 401 → Wylogowanie (edge case)
      console.log(`[MSW] Case 2: Always 401 (edge case)`);
      return res(ctx.status(401));
    }

    if (currentTestCase === 'case3') {
      // Case 3: 401 → Refresh 200 → Retry 200 → Sukces
      if (programDetailsCallCount === 1) {
        console.log(`[MSW] Case 3: First call → 401`);
        return res(ctx.status(401));
      } else {
        console.log(`[MSW] Case 3: Retry call → 200 SUCCESS`);
        return res(
          ctx.status(200),
          ctx.json({
            programExtId: 'lid700888192036986',
            name: 'Clarence',
            description: 'Test program details...',
          }),
        );
      }
    }

    // Default: 401
    return res(ctx.status(401));
  },
);

// Mock dla refresh token
const mockRefreshResponse = rest.post(
  '/gpapi2/auth/refresh',
  (_req, res, ctx) => {
    refreshCallCount++;
    console.log(`[MSW] Refresh token call #${refreshCallCount}`);
    console.log(`[MSW] Current test case: ${currentTestCase}`);

    if (currentTestCase === 'case1') {
      // Case 1: 401 → Refresh 401 → Wylogowanie
      console.log(`[MSW] Case 1: Refresh → 401`);
      return res(ctx.status(401));
    }

    if (currentTestCase === 'case2' || currentTestCase === 'case3') {
      // Case 2 & 3: 401 → Refresh 200 → ...
      console.log(`[MSW] Case 2/3: Refresh → 200 SUCCESS`);
      return res(
        ctx.status(200),
        ctx.json({
          refreshToken: 'new-fake-refresh-token-12345',
        }),
      );
    }

    // Default: 401
    return res(ctx.status(401));
  },
);

// Funkcja do zmiany test case
window.testCase = currentTestCase;
window.switchTestCase = (newCase: string) => {
  currentTestCase = newCase;
  window.testCase = newCase;
  programDetailsCallCount = 0;
  refreshCallCount = 0;
  console.log(`[MSW] Switched to ${newCase}`);
};

export const worker = setupWorker(
  mockProgramDetailsResponse,
  mockRefreshResponse,
);

window.msw = {
  worker,
  rest,
};
