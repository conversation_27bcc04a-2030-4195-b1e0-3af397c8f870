import { rest, setupWorker } from 'msw';
declare global {
  interface Window {
    msw: any;
  }
}

// Mock dla testowania 401 responses - symuluje nieważną sesję użytkownika
const mockChannelsResponse = rest.get(
  '/gpapi2/live/channel/all',
  (_req, res, ctx) => {
    console.log('[MSW] Mocking channels request with 401');
    return res(ctx.status(401));
  },
);

// Mock refresh token endpoint - zwraca 401 żeby symulować nieważny refresh token
const mockRefreshResponse = rest.post(
  '/gpapi2/auth/refresh',
  (_req, res, ctx) => {
    console.log('[MSW] Mocking refresh request with 401');
    return res(ctx.status(401));
  },
);

// Mock innych endpointów które mogą być wywoływane podczas testu
const mockHouseholdResponse = rest.get(
  '/gpapi2/core/household/household',
  (_req, res, ctx) => {
    console.log('[MSW] Mocking household request with 401');
    return res(ctx.status(401));
  },
);

const mockPmsTokenResponse = rest.get(
  '/gpapi2/core/device/pms-token',
  (_req, res, ctx) => {
    console.log('[MSW] Mocking pms-token request with 401');
    return res(ctx.status(401));
  },
);

export const worker = setupWorker(
  mockChannelsResponse,
  mockRefreshResponse,
  mockHouseholdResponse,
  mockPmsTokenResponse,
);

window.msw = {
  worker,
  rest,
};
