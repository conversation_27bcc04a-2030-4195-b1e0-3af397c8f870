import { useMutation, useQuery, useQueryClient } from 'react-query';

import { newApiClient } from 'services/api/newApi';
import { Endpoint } from 'services/api/types';
import { useToasts } from 'services/toasts';
import { useChannels } from 'routes/Channels/hooks';

import { ENDPOINTS } from './enpoints';
import {
  RecordingDeleteParams,
  RecordingDetails,
  RecordingDetailsParams,
  RecordingListResponse,
  RecordingOrderRequest,
  RecordingOrderResponse,
  RecordingsByChannelParams,
  RecordingsByChannelResponse,
  RecordingsByDateParams,
  RecordingsByDateResponse,
  RecordingSeriesParams,
  RecordingSeriesResponse,
  RecordingsRequest,
  RecordingStatus,
  SeriesDeleteParams,
  SeriesOrderRequest,
} from './types';
import { npvrKeys } from './keys';

export const useRecordingsQuery = (params: RecordingsRequest) => {
  const { recordingStatus, ...rest } = params;

  const url: Endpoint = {
    ...ENDPOINTS.GET_RECORDINGS,
    url: `${ENDPOINTS.GET_RECORDINGS.url}`,
  };
  const { allChannels } = useChannels();

  return useQuery(
    npvrKeys.recordings(params),
    async ({ signal }) => {
      const response = await newApiClient.get<
        Omit<RecordingsRequest, 'recordingStatus'>,
        RecordingListResponse
      >(url, { recordingStatus, ...rest }, signal);

      return {
        recordingList: response.data.recordingList?.map((recording) =>
          recording.name ? recording : { ...recording, name: '' },
        ),
        ...response.data,
      };
    },
    {
      useErrorBoundary: false,
      select: (recordings: RecordingListResponse) => {
        const responseWithoutDeleted = {
          ...recordings,
          recordingList: recordings.recordingList?.filter(
            (recording) => recording.status !== RecordingStatus.DELETED,
          ),
        };

        if (allChannels.length <= 0) {
          return responseWithoutDeleted;
        }

        const availableChannelsExtIdSet = new Set(
          allChannels.map((channel) => channel.channelExtId),
        );
        return {
          recordingList: responseWithoutDeleted.recordingList?.filter(
            (recording) =>
              recording.channelExtId
                ? availableChannelsExtIdSet.has(recording.channelExtId)
                : false,
          ),

          seriesList: responseWithoutDeleted.seriesList?.filter((series) =>
            series.channelExtId
              ? availableChannelsExtIdSet.has(series.channelExtId)
              : false,
          ),
          imagePaths: responseWithoutDeleted.imagePaths,
        };
      },
    },
  );
};

export const useRecordingDeleteMutation = () => {
  const queryClient = useQueryClient();
  const { showToast } = useToasts();
  return useMutation(
    async (params: RecordingDeleteParams) => {
      const response = await newApiClient.delete(ENDPOINTS.DELETE_RECORDING, {
        recordingExtId: params.recordingExtId,
      });

      return response.data;
    },
    {
      onSuccess: (_unusedA, params) => {
        const { status } = params;
        if (status === RecordingStatus.RECORDED) {
          showToast('DELETE_RECORDING');
        } else if (status === RecordingStatus.SCHEDULED) {
          showToast('DELETE_RECORDING_ORDER');
        }
        return queryClient.invalidateQueries(npvrKeys.all);
      },
      onError: (_unusedA, params) => {
        const { status } = params;
        if (status === RecordingStatus.RECORDED) {
          return showToast('DELETE_RECORDING_ERROR');
        } else if (status === RecordingStatus.SCHEDULED) {
          return showToast('DELETE_RECORDING_ORDER_ERROR');
        }
      },
    },
  );
};

export const useSeriesDeleteMutation = () => {
  const queryClient = useQueryClient();
  const { showToast } = useToasts();
  return useMutation(
    async (params: SeriesDeleteParams) => {
      const response = await newApiClient.delete(ENDPOINTS.DELETE_SERIES, {
        recordingExtId: params.recordingExtId,
      });

      return response.data;
    },
    {
      onSuccess: (_unusedA, params) => {
        const { status } = params;
        if (status === RecordingStatus.RECORDED) {
          showToast('DELETE_SERIES');
        } else if (status === RecordingStatus.SCHEDULED) {
          showToast('DELETE_SERIES_ORDER');
        }
        return queryClient.invalidateQueries(npvrKeys.all);
      },
      onError: (_unusedA, params) => {
        const { status } = params;
        if (status === RecordingStatus.RECORDED) {
          return showToast('DELETE_SERIES_ERROR');
        } else if (status === RecordingStatus.SCHEDULED) {
          return showToast('DELETE_SERIES_ORDER_ERROR');
        }
      },
    },
  );
};

export const useRecordingDetailsQuery = (params: RecordingDetailsParams) => {
  return useQuery(
    npvrKeys.recordingDetails(params),
    async ({ signal }) => {
      const response = await newApiClient.get<
        RecordingDetailsParams,
        RecordingDetails
      >(ENDPOINTS.GET_RECORDING, params, signal);
      return response.data;
    },
    { useErrorBoundary: false },
  );
};

export const useRecordingSeriesQuery = (
  params: RecordingSeriesParams | undefined,
) => {
  return useQuery(
    npvrKeys.recordingSeries(params),
    async ({ signal }) => {
      const response = await newApiClient.get<
        RecordingSeriesParams,
        RecordingSeriesResponse
      >(ENDPOINTS.GET_SERIES, params, signal);

      return response.data;
    },
    {
      enabled: Boolean(params),
      select: (series: RecordingSeriesResponse) => {
        return {
          ...series,
          recordings: series.recordings.filter(
            (recording) => recording.status !== RecordingStatus.DELETED,
          ),
        };
      },
    },
  );
};

export const useRecordingOrderMutation = () => {
  const queryClient = useQueryClient();

  return useMutation(
    async (data: RecordingOrderRequest) => {
      const response = await newApiClient.post<
        RecordingOrderRequest,
        RecordingOrderResponse,
        {}
      >(ENDPOINTS.POST_RECORDING, data);
      return response;
    },
    {
      onSuccess: () => {
        return Promise.all([queryClient.invalidateQueries(npvrKeys.all)]);
      },
    },
  );
};

export const useSeriesOrderMutation = () => {
  const queryClient = useQueryClient();

  return useMutation(
    async (data: SeriesOrderRequest) => {
      const response = await newApiClient.post<SeriesOrderRequest, {}, {}>(
        ENDPOINTS.POST_SERIES,
        data,
      );
      return response;
    },
    {
      onSuccess: () => {
        return Promise.all([queryClient.invalidateQueries(npvrKeys.all)]);
      },
    },
  );
};

export const useRecordingsByChannelQuery = (
  params: RecordingsByChannelParams,
) => {
  return useQuery(
    npvrKeys.recordingsByChannel(params),
    async ({ signal }) => {
      const response = await newApiClient.get<{}, RecordingsByChannelResponse>(
        ENDPOINTS.GET_RECORDINGS_BY_CHANNEL,
        params,
        signal,
      );

      return response.data;
    },
    {
      staleTime: ENDPOINTS.GET_RECORDINGS_BY_CHANNEL.RQStaleTime,
      cacheTime: ENDPOINTS.GET_RECORDINGS_BY_CHANNEL.RQCacheTime,
    },
  );
};

export const useRecordingsByDateQuery = (
  params: RecordingsByDateParams,
  isAuthenticated: boolean,
) => {
  return useQuery(
    npvrKeys.recordingsByDate(params),
    async ({ signal }) => {
      const response = await newApiClient.get<{}, RecordingsByDateResponse>(
        ENDPOINTS.GET_RECORDINGS_BY_DATE,
        params,
        signal,
        'json',
      );
      return response.data;
    },
    {
      staleTime: ENDPOINTS.GET_RECORDINGS_BY_DATE.RQStaleTime,
      cacheTime: ENDPOINTS.GET_RECORDINGS_BY_DATE.RQCacheTime,
      enabled: isAuthenticated,
      useErrorBoundary: false,
    },
  );
};

export const useGetRecordingParentalControlMutation = () => {
  return useMutation(async (params: RecordingDetailsParams) => {
    const response = await newApiClient.get<{}, RecordingDetails>(
      ENDPOINTS.GET_RECORDING,
      params,
    );
    return response.data.prLevel;
  });
};
