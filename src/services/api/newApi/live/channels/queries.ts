import { useMutation, useQuery } from 'react-query';
import { useCallback } from 'react';

import { newApiClient } from 'services/api/newApi';
import { useConfig } from 'services/config';
import { isUnauthorizedError } from 'services/error/guards';
import { useAuthenticationStatus } from 'services/user';
import { Endpoint } from 'services/api/types';
import { REFRESH_SESSION_INTERVAL } from 'services/api/oldApi';
import { getPublicAssetUrl } from 'utils/url';

import {
  Channel,
  ChannelListResponse,
  ChannelPlayInfoParams,
  ChannelPlayInfoResponse,
  ChannelTimeShiftingPlayInfoParams,
  CloseLiveSessionParams,
  RefreshLiveSessionMutationParams,
  RefreshLiveSessionQueryParams,
  RegionalTvListResponse,
} from './types';
import { channelsKeys } from './keys';
import { ENDPOINTS } from './endpoints';

import {
  getManifestResponse,
  getValidStreamUrl,
  handleRedirectStream,
} from '../helpers';

export const useChannelsAllQuery = () => {
  const { getTechConfig } = useConfig();

  const {
    channels: { shouldIndicateHomeZone },
  } = getTechConfig();

  return useQuery(
    channelsKeys.channelAll(),
    async ({ signal }) => {
      const response = await newApiClient.get<{}, ChannelListResponse>(
        ENDPOINTS.GET_CHANNEL_ALL,
        {},
        signal,
      );
      return response.data;
    },
    {
      useErrorBoundary: false,
      cacheTime: ENDPOINTS.GET_CHANNEL_ALL.RQCacheTime,
      staleTime: ENDPOINTS.GET_CHANNEL_ALL.RQStaleTime,
      select: useCallback(
        (data: ChannelListResponse) => {
          const { channelList, imagePaths } = data;

          const channelsWithImages = channelList.map((channel) => ({
            ...channel,
            logoUrl: getPublicAssetUrl(
              imagePaths.standard,
              channel.logoImageId,
            ),
            isHomeZoneRestricted:
              shouldIndicateHomeZone === false
                ? false
                : channel.isHomeZoneRestricted,
          }));

          return channelsWithImages;
        },
        [shouldIndicateHomeZone],
      ),
    },
  );
};

export const useChannelPlayInfo = (
  params: ChannelPlayInfoParams,
  enabled: boolean = true,
  onError?: (error: unknown) => void,
) => {
  const { channelExtId, isRegionalTv, userRegion, useOptionalRegionParam } =
    params;

  const url: Endpoint = {
    ...ENDPOINTS.GET_CHANNEL_PLAY_INFO,
    url: `${ENDPOINTS.GET_CHANNEL_PLAY_INFO.url}/${channelExtId}`,
  };

  return useQuery(
    channelsKeys.channelPlayInfo(params),
    async ({ signal }) => {
      const response = await newApiClient.get<{}, ChannelPlayInfoResponse>(
        url,
        useOptionalRegionParam && {
          optionalRegion: userRegion,
        },
        signal,
      );

      return {
        ...response.data,
        streamUrl: useOptionalRegionParam
          ? await handleRedirectStream(
              response.data.streamUrl,
              response.data.redirectBypass,
            )
          : await getValidStreamUrl(
              response.data.streamUrl,
              response.data.redirectBypass,
              isRegionalTv,
              userRegion,
            ),
      };
    },
    {
      useErrorBoundary: false,
      enabled,
      onError,
      cacheTime: ENDPOINTS.GET_CHANNEL_PLAY_INFO.RQCacheTime,
      staleTime: ENDPOINTS.GET_CHANNEL_PLAY_INFO.RQStaleTime,
    },
  );
};

export const useChannelTimeShiftingPlayInfo = (
  params: ChannelTimeShiftingPlayInfoParams,
  enabled: boolean = true,
  onError?: (error: unknown) => void,
) => {
  const { channelExtId, programExtId, timeShiftingService } = params;

  const url: Endpoint = {
    ...ENDPOINTS.GET_NPVR_PLAY_INFO,
    url: `${ENDPOINTS.GET_NPVR_PLAY_INFO.url}/${channelExtId}/${programExtId}/${timeShiftingService}`,
  };

  const requestParams = params.recordingId
    ? { recordingExtId: params.recordingId }
    : {};

  return useQuery(
    channelsKeys.channelTimeShiftingPlayInfo(params),
    async ({ signal }) => {
      const response = await newApiClient.get<{}, ChannelPlayInfoResponse>(
        url,
        requestParams,
        signal,
      );

      return {
        ...response.data,
        streamUrl: await getManifestResponse(response.data.streamUrl),
      };
    },
    {
      useErrorBoundary: false,
      enabled,
      onError,
    },
  );
};

export const useRefreshLiveSessionQuery = (
  params: RefreshLiveSessionQueryParams,
) => {
  const { channelExtId, isQueryEnabled } = params;

  const url: Endpoint = {
    ...ENDPOINTS.REFRESH_LIVE_SESSION,
    url: `${ENDPOINTS.REFRESH_LIVE_SESSION.url}/${channelExtId}/session`,
  };

  return useQuery(
    channelsKeys.refreshLiveSession(params),
    async ({ signal }) => {
      const response = await newApiClient.get<{}, number>(url, {}, signal);

      return response.status;
    },
    {
      useErrorBoundary: false,
      refetchInterval: REFRESH_SESSION_INTERVAL,
      refetchIntervalInBackground: true,
      cacheTime: 0,
      staleTime: 0,
      enabled: isQueryEnabled,
    },
  );
};

export const useRefreshLiveSessionMutation = () => {
  return useMutation(async (params: RefreshLiveSessionMutationParams) => {
    const { channelExtId } = params;
    const url: Endpoint = {
      ...ENDPOINTS.REFRESH_LIVE_SESSION,
      url: `${ENDPOINTS.REFRESH_LIVE_SESSION.url}/${channelExtId}/session`,
    };
    const response = await newApiClient.get<{}, number>(url);

    return response.status;
  });
};

export const useCloseLiveSessionMutation = () => {
  return useMutation(async (params: CloseLiveSessionParams) => {
    const { channelExtId } = params;
    const url: Endpoint = {
      ...ENDPOINTS.CLOSE_LIVE_SESSION,
      url: `${ENDPOINTS.CLOSE_LIVE_SESSION.url}/${channelExtId}/session`,
    };
    const response = await newApiClient.delete(url);

    return response.data;
  });
};

export const useRegionalTvListQuery = (isAuthenticated: boolean) => {
  return useQuery(
    channelsKeys.regionalTvList(),
    async ({ signal }) => {
      const response = await newApiClient.get<{}, RegionalTvListResponse>(
        ENDPOINTS.GET_LIVE_REGIONAL_LIST,
        {},
        signal,
      );
      return response.data;
    },
    { enabled: isAuthenticated },
  );
};
