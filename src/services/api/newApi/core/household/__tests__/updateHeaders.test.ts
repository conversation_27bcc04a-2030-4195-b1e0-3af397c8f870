import { describe, it, expect, vi } from 'vitest';
import { renderHook, waitFor } from 'utils/testing';
import { useCoreHouseholdQuery } from '../queries';
import { newApiClient } from 'services/api/newApi';
import { setupMockServer } from 'services/api/mock/mock.server';

describe('updateHeaders', async () => {
  setupMockServer();

  it('should correctly update OtvDeviceInfo2 header after fetch household data', async () => {
    const headersBeforeUpdate = newApiClient['instance'].defaults.headers;
    const paramsBeforeUpdate = new URLSearchParams(
      headersBeforeUpdate['OtvDeviceInfo2']?.toString(),
    );

    expect(paramsBeforeUpdate.get('hh_tech')).toEqual('');
    expect(paramsBeforeUpdate.get('offer')).toEqual('');
    expect(paramsBeforeUpdate.get('serial_number')).toEqual('');

    const { result } = renderHook(() => useCoreHouseholdQuery(true));
    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    const headersAfterUpdate = newApiClient['instance'].defaults.headers;
    const paramsAfterUpdate = new URLSearchParams(
      headersAfterUpdate['OtvDeviceInfo2']?.toString(),
    );

    expect(paramsAfterUpdate.get('hh_tech')).toEqual('ftth');
    expect(paramsAfterUpdate.get('offer')).toEqual('offer_from_mock');
    expect(paramsAfterUpdate.get('serial_number')).toEqual(
      'UNKNOWN_WEB_TERMINAL_13333301248',
    );
  });
});
