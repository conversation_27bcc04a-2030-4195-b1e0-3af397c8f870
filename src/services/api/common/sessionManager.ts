import { AxiosResponse } from 'axios';

import { REFRESH_LOCK_TIME } from './constants';

type RequestPromise = () => Promise<AxiosResponse>;

class SessionManager {
  private isRefreshing: boolean = false;
  private refreshLockUntil: number = 0;
  private failedRequestsQueue: {
    resolve: (value: AxiosResponse | PromiseLike<AxiosResponse>) => void;
    reject: (reason?: unknown) => void;
    requestPromise: RequestPromise;
  }[] = [];

  async handleRefreshSession(
    refreshFunction: () => Promise<void>,
  ): Promise<void> {
    if (this.isRefreshing) {
      return;
    }

    const wasRefreshMade = Date.now() < this.refreshLockUntil;
    if (wasRefreshMade) {
      this.resolveQueue();
      return;
    }

    try {
      this.isRefreshing = true;
      await refreshFunction();
      this.refreshLockUntil = Date.now() + REFRESH_LOCK_TIME;

      this.resolveQueue();
    } catch (error: unknown) {
      this.rejectQueue(error);
      throw error;
    } finally {
      this.isRefreshing = false;
    }
  }

  addFailedRequestToQueue(
    requestPromise: RequestPromise,
  ): Promise<AxiosResponse> {
    return new Promise((resolve, reject) => {
      this.failedRequestsQueue.push({ resolve, reject, requestPromise });
    });
  }

  private clearQueue() {
    this.failedRequestsQueue = [];
  }

  private rejectQueue(error: unknown) {
    this.failedRequestsQueue.forEach((request) => {
      request.reject(error);
    });
    this.clearQueue();
  }

  private resolveQueue() {
    this.failedRequestsQueue.forEach((request) => {
      request.resolve(request.requestPromise());
    });
    this.clearQueue();
  }

  get isRefreshingNow(): boolean {
    return this.isRefreshing;
  }
}

export const sessionManager = new SessionManager();
