import { AxiosError, AxiosInstance } from 'axios';

import { isUnauthorizedError } from 'services/error';

import { sessionManager } from './sessionManager';

import { ENDPOINTS } from '../newApi/auth/endpoints';

export function setupUnauthorizedInterceptor(
  instance: AxiosInstance,
  refreshSession: () => Promise<void>,
) {
  instance.interceptors.response.use(
    (response) => response,
    async (error: AxiosError) => {
      const originalRequest = error.config;

      if (!originalRequest) {
        return Promise.reject(
          new Error('Original request config is missing or undefined.'),
        );
      }

      if (!isUnauthorizedError(error)) {
        return Promise.reject(error);
      }

      const isErrorOnRefreshSessionRequest =
        originalRequest?.url === ENDPOINTS.POST_REFRESH.url;

      if (isErrorOnRefreshSessionRequest) {
        return Promise.reject(error);
      }

      if ((originalRequest as any).__isRetryAfterRefresh) {
        (error as any).__refreshAttempted = true;
        return Promise.reject(error);
      }

      if (sessionManager.isRefreshingNow) {
        return sessionManager.addFailedRequestToQueue(() =>
          instance(originalRequest),
        );
      }

      if (sessionManager.wasRecentRefresh) {
        return sessionManager.addFailedRequestToQueue(() =>
          instance(originalRequest),
        );
      }

      try {
        await sessionManager.handleRefreshSession(() => refreshSession());

        try {
          (originalRequest as any).__isRetryAfterRefresh = true;
          return await instance(originalRequest);
        } catch (retryError: unknown) {
          if (isUnauthorizedError(retryError)) {
            (retryError as any).__refreshAttempted = true;
          }
          return Promise.reject(retryError);
        }
      } catch (refreshError: unknown) {
        (error as any).__refreshAttempted = true;
        return Promise.reject(error);
      }
    },
  );
}
