import { AxiosError, AxiosInstance } from 'axios';

import { isUnauthorizedError } from 'services/error';

import { sessionManager } from './sessionManager';

import { ENDPOINTS } from '../newApi/auth/endpoints';

export function setupUnauthorizedInterceptor(
  instance: AxiosInstance,
  refreshSession: () => Promise<void>,
) {
  instance.interceptors.response.use(
    (response) => response,
    async (error: AxiosError) => {
      const originalRequest = error.config;

      if (!originalRequest) {
        return Promise.reject(
          new Error('Original request config is missing or undefined.'),
        );
      }

      if (!isUnauthorizedError(error)) {
        return Promise.reject(error);
      }

      const isErrorOnRefreshSessionRequest =
        originalRequest?.url === ENDPOINTS.POST_REFRESH.url;

      if (isErrorOnRefreshSessionRequest) {
        return Promise.reject(error);
      }

      // <PERSON><PERSON><PERSON> to retry po udanym refresh i nadal dostaje 401, oznacz i wyloguj
      if ((originalRequest as any).__isRetryAfterRefresh) {
        console.log(
          '[Interceptor] Retry after refresh failed with 401 - setting __refreshAttempted',
        );
        (error as any).__refreshAttempted = true;
        return Promise.reject(error);
      }

      if (sessionManager.isRefreshingNow) {
        return sessionManager.addFailedRequestToQueue(() =>
          instance(originalRequest),
        );
      }

      if (sessionManager.wasRecentRefresh) {
        return sessionManager.addFailedRequestToQueue(() =>
          instance(originalRequest),
        );
      }

      try {
        await sessionManager.handleRefreshSession(() => refreshSession());

        try {
          // Oznacz request jako retry po refresh
          (originalRequest as any).__isRetryAfterRefresh = true;

          console.log(
            '[Interceptor] Retrying original request after successful refresh',
          );
          return await instance(originalRequest);
        } catch (retryError: unknown) {
          console.log('[Interceptor] Retry failed with error:', retryError);
          console.log(
            '[Interceptor] isUnauthorizedError(retryError):',
            isUnauthorizedError(retryError),
          );

          // Jeśli retry też dostaje 401, oznacz że refresh był próbowany
          if (isUnauthorizedError(retryError)) {
            (retryError as any).__refreshAttempted = true;
            console.log(
              '[Interceptor] Setting __refreshAttempted=true on retry error',
              retryError,
            );
          }
          return Promise.reject(retryError);
        }
      } catch (refreshError: unknown) {
        // Refresh się nie udał - oznacz i wyloguj
        (error as any).__refreshAttempted = true;
        return Promise.reject(error);
      }
    },
  );
}
