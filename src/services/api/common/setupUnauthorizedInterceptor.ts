import { AxiosError, AxiosInstance } from 'axios';

import { isUnauthorizedError } from 'services/error';

import { sessionManager } from './sessionManager';

import { ENDPOINTS } from '../newApi/auth/endpoints';

export function setupUnauthorizedInterceptor(
  instance: AxiosInstance,
  refreshSession: () => Promise<void>,
) {
  instance.interceptors.response.use(
    (response) => response,
    async (error: AxiosError) => {
      const originalRequest = error.config;

      if (!originalRequest) {
        return Promise.reject(
          new Error('Original request config is missing or undefined.'),
        );
      }

      if (!isUnauthorizedError(error)) {
        return Promise.reject(error);
      }

      const isErrorOnRefreshSessionRequest =
        originalRequest?.url === ENDPOINTS.POST_REFRESH.url;

      if (isErrorOnRefreshSessionRequest) {
        return Promise.reject(error);
      }

      if (sessionManager.isRefreshingNow) {
        return sessionManager.addFailedRequestToQueue(() =>
          instance(originalRequest),
        );
      }

      try {
        await sessionManager.handleRefreshSession(() => {
          return refreshSession();
        });
        return await instance(originalRequest);
      } catch {
        return Promise.reject(error);
      }
    },
  );
}
