export enum EventType {
  START = 'START',
  PERIODIC = 'PERIODIC',
}

export enum Zone {
  NEO = 'NEO',
  PL = 'PL',
  OTHER = 'OTHER',
}

export interface BasePmsReport {
  secret: string;
  videoId: string;
  deviceSerialNumber: string;
  accessType: 'TVE_WEB';
  platform: 'WWW';
  messageTimestamp: string;
  orangeApplicationVersion: string;
  deviceModel: string;
}

export interface PeriodicPmsReport extends BasePmsReport {
  eventType: EventType;
  deviceManufacturer: string;
  osVersion?: string;
  browserVersion?: string;
  hhTech: string;
  offer?: string;
  screenResolution: string;
}

export interface DiagnosticTestPmsReport extends BasePmsReport {
  homezone: boolean;
  zone: Zone | undefined;
  dnsConnection: boolean;
  cdnOplCheck: boolean;
  faultyEdgeCaches?: Array<{ url: string }>;
}
