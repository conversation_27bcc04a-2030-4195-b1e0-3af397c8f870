import { useMemo } from 'react';
import {
  MutationCache,
  QueryCache,
  QueryClient,
  QueryClientProvider,
} from 'react-query';
import { ReactQueryDevtools } from 'react-query/devtools';

import {
  getUserErrorType,
  isAxiosError,
  isHouseholdSuspendError,
  isReadOnlyMaintenanceError,
  isRequestHeaderError,
  isServerError,
  isUnauthorizedError,
  useErrorScreen,
} from 'services/error';
import { useLogger } from 'services/logger';
import { deleteRefreshToken } from 'services/user/RefreshTokenHandlers';
import { routes } from 'routes/routes-map';
import { useAuthenticationStatus } from 'services/user';

import { ApiProviderProps } from './types';
import { STALE_TIME_IN_MILLISECONDS } from './constants';

export const ApiProvider = (props: ApiProviderProps) => {
  const { children } = props;

  const { logger } = useLogger();
  const { showErrorModal } = useErrorScreen();
  const { setAuthenticated } = useAuthenticationStatus();

  console.log('[ApiProvider] Component render', {
    timestamp: new Date().toISOString(),
    currentPath: window.location.pathname,
  });

  // Do not create new QueryClient when AuthenticationProvider changed, otherwise
  // after client login/logout we are loosing current queryCache and invalidateQuery
  // can not works properly
  const queryClient = useMemo(() => {
    console.log('[ApiProvider] Creating QueryClient (useMemo)', {
      timestamp: new Date().toISOString(),
    });

    return new QueryClient({
      queryCache: new QueryCache({
        onError: (error: unknown) => {
          const isLoginPage = window.location.pathname.includes('/login');

          console.log('[ApiProvider] QueryCache.onError triggered', {
            timestamp: new Date().toISOString(),
            error: error,
            errorMessage: (error as any)?.message,
            isLoginPage,
            pathname: window.location.pathname,
            refreshAttempted: (error as any).__refreshAttempted,
            isUnauthorized: isUnauthorizedError(error),
          });

          if (
            isUnauthorizedError(error) &&
            !isLoginPage &&
            (error as any).__refreshAttempted
          ) {
            console.log(
              '[ApiProvider] UNAUTHORIZED - Starting logout process',
              {
                timestamp: new Date().toISOString(),
                willNavigateTo: routes.login,
              },
            );

            logger.error('User session is not longer valid');
            setAuthenticated(false);
            deleteRefreshToken();

            // NIE ROBIMY NAVIGATE TUTAJ - pozwalamy PrivateRoutes to obsłużyć
            console.log(
              '[ApiProvider] Set authenticated to false - PrivateRoutes will handle redirect',
              {
                timestamp: new Date().toISOString(),
              },
            );

            return;
          }

          if (isReadOnlyMaintenanceError(error)) {
            console.log('[ApiProvider] Read-only maintenance error', {
              timestamp: new Date().toISOString(),
            });
            logger.error('System is in maintenance read only mode');
            showErrorModal(getUserErrorType(error));
          }

          if (isHouseholdSuspendError(error)) {
            console.log('[ApiProvider] Household suspended error', {
              timestamp: new Date().toISOString(),
            });
            setAuthenticated(false);
            showErrorModal('HOUSEHOLD_SUSPENDED_ON_ACTION');
          }
        },
      }),
      mutationCache: new MutationCache({
        onError: (error: unknown, _unusedA, _unusedB, mutation) => {
          console.log('[ApiProvider] MutationCache.onError', {
            timestamp: new Date().toISOString(),
            error,
            isHouseholdSuspend: isHouseholdSuspendError(error),
            mutationMeta: mutation.meta,
          });

          if (isHouseholdSuspendError(error)) {
            const selectedError = mutation.meta?.isLoginMutation
              ? 'HOUSEHOLD_SUSPENDED_ON_LOGIN'
              : 'HOUSEHOLD_SUSPENDED_ON_ACTION';
            setAuthenticated(false);
            showErrorModal(selectedError);
          }
        },
      }),

      defaultOptions: {
        queries: {
          refetchOnWindowFocus: false,
          refetchOnReconnect: false,
          notifyOnChangeProps: 'tracked',
          staleTime: STALE_TIME_IN_MILLISECONDS,
          retry: false,
          useErrorBoundary: (error) => {
            return (
              isAxiosError(error) &&
              !isReadOnlyMaintenanceError(error) &&
              (isServerError(error) ||
                isRequestHeaderError(error) ||
                !error.response?.status)
            );
          },
        },
      },
    });
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
};
