import { useEffect, useState } from 'react';
import { mergeWith } from 'lodash';

import { useAuthenticationStatus } from 'services/user/AuthenticationStatusContext';
import { useCoreHouseholdQuery } from 'services/api/newApi/core/household';
import {
  AppConfig,
  HHTech,
  HHTechConfiguration,
  TechConfigContent,
} from 'services/api/newApi/core/appConfigs';

import defaultTechConfigFetchedBeforeBuild from './defaultTechConfig.json';

type ConfigFile = {
  appConfigList: AppConfig[];
};

const defaultTechConfigFromFile = (
  (defaultTechConfigFetchedBeforeBuild as unknown as ConfigFile)
    .appConfigList[0].content as TechConfigContent
).hhtechs.find((tech: HHTech) => tech.name === 'default')!.configuration;

export const useTechsConfig = (techsConfig: TechConfigContent) => {
  const { isAuthenticated } = useAuthenticationStatus();
  const { data: householdInfo } = useCoreHouseholdQuery(isAuthenticated);
  const userHHTech = householdInfo?.hhTech;
  const userOffer = householdInfo?.offer;
  const [techConfig, setTechConfig] = useState<HHTechConfiguration>(
    defaultTechConfigFromFile,
  );

  const mergeCustomizer = (
    _objValue: HHTechConfiguration,
    srcValue: HHTechConfiguration[keyof HHTechConfiguration],
  ) => {
    if (typeof srcValue === 'boolean') {
      return srcValue;
    }
  };

  useEffect(() => {
    const findTechByName = (techName: string) =>
      techsConfig?.hhtechs.find((tech) => tech.name === techName);
    const techDefaultConfigFromApi = findTechByName('default')?.configuration;

    // If userOffer exist use it instead userHHTech to define tech_configuration
    const appConfigSelector = userOffer || userHHTech;
    if (isAuthenticated && techsConfig && appConfigSelector) {
      const techConfigBasedOnUser =
        findTechByName(appConfigSelector)?.configuration;

      const mergedTechConfig = mergeWith(
        {},
        defaultTechConfigFromFile,
        ...[techDefaultConfigFromApi, techConfigBasedOnUser],
        mergeCustomizer,
      );
      return setTechConfig(mergedTechConfig);
    }

    const notAuthenticatedUserConfig = mergeWith(
      {},
      defaultTechConfigFromFile,
      techDefaultConfigFromApi,
      mergeCustomizer,
    );

    return setTechConfig(notAuthenticatedUserConfig);
  }, [techsConfig, userHHTech, isAuthenticated, userOffer]);

  return techConfig;
};
