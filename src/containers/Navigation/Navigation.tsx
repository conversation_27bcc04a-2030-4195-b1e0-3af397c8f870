import { memo, MouseEvent, useCallback, useEffect, useState } from 'react';
import { useIntl } from 'react-intl';
import { useLocation } from 'react-router-dom';

import { routes } from 'routes/routes-map';
import { NavLink } from 'components/Typography';
import { HamburgerButton } from 'components/Buttons/HamburgerButton';
import { useAppLayoutMode } from 'services/appLayoutMode';
import { PlayerMode } from 'features/Player';
import { useAuthenticationStatus } from 'services/user/AuthenticationStatusContext';
import { useUserProfile } from 'services/user';
import { useModalNpvr } from 'services/user/ModalNpvr';
import { useRegionalTvContext } from 'services/regionalTv';
import { useConfig } from 'services/config';

import {
  Backdrop,
  Container,
  HamburgerWrapper,
  NavigationItem,
  NavigationList,
} from './styles';
import messages from './messages';
import { useRouteOrigin } from './context';

const NavigationRaw = ({ ...rest }) => {
  const { formatMessage } = useIntl();
  const location = useLocation();
  const { setMode, mode } = useAppLayoutMode();
  const { setOriginRoute } = useRouteOrigin();
  const { isAuthenticated } = useAuthenticationStatus();
  const { isNpvrService } = useUserProfile();
  const { showNpvrModal } = useModalNpvr();
  const { isRegionalSelectViewVisible, setIsRegionalSelectViewVisible } =
    useRegionalTvContext();

  const { getTechConfig } = useConfig();
  const techConfig = getTechConfig();

  const [isMenuOpen, setIsMenuOpen] = useState<boolean>(false);
  const [selectedCategory, setSelectedCategory] = useState<{
    id: string;
    defaultMessage: string;
  }>(messages.home);

  const navItems = [
    {
      to: routes.home,
      name: messages.home,
    },
    {
      to: routes.channels,
      name: messages.channels,
    },
    {
      to: routes.program,
      name: messages.program,
    },
    {
      to: routes.vod,
      name: messages.vod,
      isHidden: !techConfig.mainTabs?.isVodTabVisible,
    },
    {
      to: routes.recordings,
      name: messages.recordings,
      isHidden: !techConfig.mainTabs?.isNpvrTabVisible,
    },
  ];

  useEffect(() => {
    setOriginRoute(location.pathname);

    switch (location.pathname) {
      case routes.home:
        return setSelectedCategory(messages.home);
      case `/${routes.channels}`:
        return setSelectedCategory(messages.channels);
      case `/${routes.program}`:
        return setSelectedCategory(messages.program);
      case `/${routes.recordings}`:
        return setSelectedCategory(messages.recordings);
      case `/${routes.vod}`:
        return setSelectedCategory(messages.vod);
      default:
        return setSelectedCategory(messages.home);
    }
  }, [location.pathname, setOriginRoute]);

  const toggleOpenMenu = useCallback(() => setIsMenuOpen((prev) => !prev), []);
  const handleCloseMenu = useCallback(() => setIsMenuOpen(false), []);
  const handleNavItemClick = useCallback(
    (event: MouseEvent<HTMLAnchorElement>) => {
      const shouldShowNpvrModal =
        isAuthenticated &&
        event.currentTarget.pathname === routes.recordings &&
        !isNpvrService;

      if (shouldShowNpvrModal) {
        event.preventDefault();
        showNpvrModal();
        return handleCloseMenu();
      }

      handleCloseMenu();

      if (mode === PlayerMode.Expanded) {
        setMode(PlayerMode.Background);
      }
      if (isRegionalSelectViewVisible) {
        setIsRegionalSelectViewVisible(false);
      }
    },
    [
      isAuthenticated,
      isNpvrService,
      handleCloseMenu,
      mode,
      isRegionalSelectViewVisible,
      showNpvrModal,
      setMode,
      setIsRegionalSelectViewVisible,
    ],
  );

  return (
    <>
      <Backdrop $isMenuOpen={isMenuOpen} onClick={handleCloseMenu} />
      <Container data-testid='navigation-container' {...rest}>
        <HamburgerWrapper $isMenuOpen={isMenuOpen}>
          <HamburgerButton
            onClick={toggleOpenMenu}
            isOpen={isMenuOpen}
            text={formatMessage(selectedCategory)}
          />
        </HamburgerWrapper>
        <NavigationList data-testid='navigation-list' $isMenuOpen={isMenuOpen}>
          {navItems.map(
            ({ to, name, isHidden }) =>
              !Boolean(isHidden) && (
                <NavigationItem data-testid='navigation-item' key={to}>
                  <NavLink
                    to={to}
                    $sizeSmall={true}
                    onClick={(event) => handleNavItemClick(event)}
                  >
                    {formatMessage(name)}
                  </NavLink>
                </NavigationItem>
              ),
          )}
        </NavigationList>
      </Container>
    </>
  );
};

export const Navigation = memo(NavigationRaw);
