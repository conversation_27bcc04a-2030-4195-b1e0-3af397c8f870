import { FC, PropsWithChildren, useCallback, useMemo, useRef } from 'react';
import { useIntl } from 'react-intl';

import { FavoriteButton } from 'components/Buttons/FavoriteButton/FavoriteButton';
import { getPublicAssetUrl } from 'utils/url/asset';
import { IMAGE_URL } from 'services/api/oldApi';
import { Image } from 'components/Image';
import { Text } from 'components/Typography';
import { IconHomeArea, IconLock } from 'components/Icons';
import { IconPlayerPlay, IconPlayerStartover } from 'components/Icons/player';
import { useAuthenticationStatus, useUserProfile } from 'services/user';
import { useHover } from 'hooks/useHover';
import { Tooltip } from 'components/Tooltip';
import { useModalNpvr } from 'services/user/ModalNpvr';
import { useConfig } from 'services/config';
import { useDailyEpg } from 'routes/Program/hook';

import * as S from './styles';
import { ChannelProps } from './types';
import {
  useChannelFavorite,
  useChannelLazyLoad,
  useChannelPlay,
  useChannelProgram,
  useChannelRecording,
} from './hooks';
import { messages } from './messages';

export const Channel: FC<PropsWithChildren<ChannelProps>> = ({ channel }) => {
  const {
    channelExtId,
    channelNumber,
    name,
    isSubscribed,
    isHomeZoneRestricted,
  } = channel;

  const { withVisibleNpvrFeatures } = useConfig().getTechConfig();
  const { formatMessage } = useIntl();
  const { isNpvrService } = useUserProfile();
  const { currentProgram, programTimeRange, isStartoverAvailable, isBlackout } =
    useChannelProgram(channel);
  const { isFavorite, toggleFavorite } = useChannelFavorite(
    channelExtId,
    channelNumber,
  );
  const { getTechConfig } = useConfig();
  const {
    channels: {
      assets: [
        {
          channelUnavailableWording: { content: channelNotAvailableWording },
        },
      ],
    },
  } = getTechConfig();

  const { isAuthenticated } = useAuthenticationStatus();
  const { isRecordingNow } = useChannelRecording(
    isAuthenticated,
    currentProgram?.programExtId,
  );
  const { hover, handleMouseEnter, handleMouseLeave } = useHover();
  const { handlePlayChannelStartover, handlePlayChannel } =
    useChannelPlay(channel);
  const { epg } = useDailyEpg();
  const { showNpvrModal } = useModalNpvr();
  const channelRef = useRef<HTMLDivElement>(null);
  const { isChannelLoaded } = useChannelLazyLoad(channelRef);

  const lockOrRecordingNow = useMemo(() => {
    if (!isSubscribed)
      return (
        <Tooltip
          content={channelNotAvailableWording}
          width={244}
          tooltipShift={32}
          renderIcon={() => <IconLock />}
        />
      );
    else if (isRecordingNow && hover) return <S.RecordingNowDot />;
  }, [channelNotAvailableWording, hover, isRecordingNow, isSubscribed]);

  const isStartoverDisabled = useMemo(
    () => !isAuthenticated,
    [isAuthenticated],
  );

  const playStartover = useCallback(() => {
    if (isNpvrService) {
      return handlePlayChannelStartover();
    }
    return showNpvrModal();
  }, [handlePlayChannelStartover, isNpvrService, showNpvrModal]);

  const tileContentOnHover = useMemo(() => {
    return (
      <>
        {(channel.isSubscribed || !isAuthenticated) && (
          <S.ChannelControls>
            {isStartoverAvailable && withVisibleNpvrFeatures && (
              <S.ButtonWrapper disabled={isStartoverDisabled}>
                <S.ChannelButton
                  disabled={isStartoverDisabled}
                  onClick={playStartover}
                >
                  <IconPlayerStartover width={22} height={22} />
                </S.ChannelButton>
              </S.ButtonWrapper>
            )}
            <S.ChannelButton onClick={handlePlayChannel}>
              <IconPlayerPlay />
            </S.ChannelButton>
          </S.ChannelControls>
        )}
        <S.ProgramTime data-testid='Channel-ProgramTime'>
          <Text $sizeSmall>{programTimeRange}</Text>
        </S.ProgramTime>
      </>
    );
  }, [
    channel.isSubscribed,
    handlePlayChannel,
    isAuthenticated,
    isStartoverAvailable,
    isStartoverDisabled,
    playStartover,
    programTimeRange,
    withVisibleNpvrFeatures,
  ]);

  return (
    <S.ChannelWrapper ref={channelRef} data-testid='Channel-ChannelWrapper'>
      <>
        <S.TileWrapper
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
          $backgroundImage={
            hover && epg?.imagePaths?.thumb && currentProgram?.image
              ? getPublicAssetUrl(epg?.imagePaths.thumb, currentProgram.image)
              : ''
          }
        >
          <S.IconsTop>
            {lockOrRecordingNow}
            <S.FavoriteButtonContainer>
              <FavoriteButton
                isFavorite={isFavorite}
                handleClick={toggleFavorite}
                isVisible={hover}
              />
            </S.FavoriteButtonContainer>
          </S.IconsTop>
          <S.CenterWrapper>
            {isChannelLoaded && (
              <Image src={channel.logoUrl} alt={name} placeholderScale={0.5} />
            )}
            {hover && tileContentOnHover}
          </S.CenterWrapper>
          <S.IconsBottom>
            {isHomeZoneRestricted && (
              <Tooltip
                content={formatMessage(messages.homeZoneInfo)}
                width={244}
                tooltipShift={32}
                renderIcon={() => <IconHomeArea width={22} height={20} />}
              />
            )}
          </S.IconsBottom>
        </S.TileWrapper>
        <S.ChannelDetails>
          <S.ChannelName>
            <Text $sizeMedium title={name}>
              {name}
            </Text>
          </S.ChannelName>
          {isBlackout ? (
            <S.ProgramBlackout data-testid='Channel-ProgramBlackout'>
              <Text $sizeXXSmall $highlight>
                {formatMessage(messages.programBlackout)}
              </Text>
            </S.ProgramBlackout>
          ) : (
            <S.ProgramName data-testid='Channel-ProgramName'>
              <Text $sizeXXSmall $primary>
                {currentProgram?.name}
              </Text>
            </S.ProgramName>
          )}
        </S.ChannelDetails>
      </>
    </S.ChannelWrapper>
  );
};
